/**
 * Typography System
 *
 * This file centralizes all typography-related styles for the application.
 * Inspired by ChatGPT's typography system with optimizations for Chinese text.
 *
 * The font stack prioritizes:
 * 1. System fonts for optimal performance and native feel
 * 2. Noto Sans SC for excellent Chinese character support
 * 3. Appropriate fallbacks for various operating systems
 */

/* Font Family Variables - 性能优化版本 */
:root {
  /* Primary UI Font - 优先使用系统字体，减少字体加载时间 */
  --font-family-sans: -apple-system, BlinkMacSystemFont, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei',
                      'Source Han Sans SC', 'Noto Sans SC', 'Helvetica Neue', 'Segoe UI', Arial, sans-serif;

  /* 衬线字体 - 优先系统字体 */
  --font-family-serif: 'STSong', 'SimSun', 'Source Han Serif SC', 'Noto Serif SC', serif;

  /* Monospace Font for Code - 优先系统等宽字体 */
  --font-family-mono: 'SF Mono', '<PERSON><PERSON>', '<PERSON>solas', 'Monaco', 'Liberation Mono', 'Courier New', monospace;

  /* 字体加载策略 - 使用font-display优化 */
  --font-display-strategy: swap; /* 立即显示回退字体，字体加载完成后替换 */

  /* Font Weight Variables */
  --font-weight-normal: 400;
  --font-weight-medium: 500;
  --font-weight-semibold: 600;
  --font-weight-bold: 700;

  /* Font Size Scale - Based on a 16px base */
  --font-size-xs: 0.75rem;     /* 12px */
  --font-size-sm: 0.875rem;    /* 14px */
  --font-size-base: 1rem;      /* 16px */
  --font-size-lg: 1.125rem;    /* 18px */
  --font-size-xl: 1.25rem;     /* 20px */
  --font-size-2xl: 1.5rem;     /* 24px */
  --font-size-3xl: 1.875rem;   /* 30px */
  --font-size-4xl: 2.25rem;    /* 36px */

  /* Line Height Scale */
  --line-height-tight: 1.25;
  --line-height-normal: 1.5;
  --line-height-relaxed: 1.75;

  /* Letter Spacing */
  --letter-spacing-tighter: -0.05em;
  --letter-spacing-tight: -0.025em;
  --letter-spacing-normal: 0;
  --letter-spacing-wide: 0.025em;
  --letter-spacing-wider: 0.05em;
}

/* 优化的字体预加载 - 仅预加载关键字体 */
@font-face {
  font-family: 'Source Han Sans SC';
  src: url('https://cdnfe-azure.summerfarm.net/fonts/source-han-sans-sc-regular.woff2') format('woff2');
  font-weight: normal;
  font-style: normal;
  font-display: var(--font-display-strategy);
  /* 仅预加载中文字符子集 */
  unicode-range: U+4E00-9FFF, U+3400-4DBF, U+20000-2A6DF, U+2A700-2B73F, U+2B740-2B81F, U+2B820-2CEAF;
}

@font-face {
  font-family: 'Source Han Sans SC';
  src: url('https://cdnfe-azure.summerfarm.net/fonts/source-han-sans-sc-medium.woff2') format('woff2');
  font-weight: 500;
  font-style: normal;
  font-display: var(--font-display-strategy);
  unicode-range: U+4E00-9FFF, U+3400-4DBF, U+20000-2A6DF, U+2A700-2B73F, U+2B740-2B81F, U+2B820-2CEAF;
}

/* Base Typography Styles - 性能优化 */
html, body {
  font-family: var(--font-family-sans);
  font-size: var(--font-size-base);
  line-height: var(--line-height-normal);
  font-weight: var(--font-weight-normal);
  /* 优化字体渲染性能 */
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-rendering: optimizeSpeed; /* 改为optimizeSpeed以提升性能 */
  color: var(--color-text-primary);
  /* 字体特性设置 */
  font-feature-settings: "kern" 1; /* 启用字距调整 */
  font-variant-ligatures: common-ligatures; /* 启用常见连字 */
}

/* Heading Styles */
h1, h2, h3, h4, h5, h6 {
  font-family: var(--font-family-sans);
  font-weight: var(--font-weight-semibold);
  line-height: var(--line-height-tight);
  margin-top: 0;
  color: var(--color-text-primary);
}

h1 {
  font-size: var(--font-size-3xl);
  letter-spacing: var(--letter-spacing-tight);
}

h2 {
  font-size: var(--font-size-2xl);
  letter-spacing: var(--letter-spacing-tight);
}

h3 {
  font-size: var(--font-size-xl);
}

h4 {
  font-size: var(--font-size-lg);
}

h5, h6 {
  font-size: var(--font-size-base);
}

/* Paragraph Styles */
p {
  margin-top: 0;
  margin-bottom: 1rem;
}

/* Link Styles */
a {
  color: var(--color-accent-blue);
  text-decoration: none;
  transition: color var(--duration-normal);
}

a:hover {
  text-decoration: underline;
}

/* UI Element Typography */
.btn, button {
  font-family: var(--font-family-sans);
  font-weight: var(--font-weight-medium);
}

input, textarea, select {
  font-family: var(--font-family-sans);
  font-size: var(--font-size-base);
}

/* Code Typography */
code, pre {
  font-family: var(--font-family-mono);
}

code {
  font-size: 0.9em;
}

/* Component-Specific Typography */

/* Header Typography */
.header-title {
  font-weight: var(--font-weight-semibold);
  font-size: var(--font-size-lg);
}

/* Sidebar Typography */
.sidebar-title {
  font-weight: var(--font-weight-semibold);
  font-size: var(--font-size-lg);
}

.conversation-item {
  font-size: var(--font-size-sm);
}

.conversation-group > div:first-child {
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-medium);
  letter-spacing: var(--letter-spacing-wide);
  text-transform: uppercase;
}

/* Message Typography */
.user-message-content {
  font-size: var(--font-size-base);
  line-height: var(--line-height-relaxed);
}

.ai-message-content {
  font-size: var(--font-size-base);
  line-height: var(--line-height-relaxed);
}

.message-footer {
  font-size: var(--font-size-xs);
  color: var(--color-text-tertiary);
}

/* Markdown Content Typography */
.markdown-content {
  line-height: var(--line-height-relaxed);
}

.markdown-content h1 {
  font-size: var(--font-size-2xl);
  margin-top: 1.5rem;
  margin-bottom: 1rem;
}

.markdown-content h2 {
  font-size: var(--font-size-xl);
  margin-top: 1.5rem;
  margin-bottom: 1rem;
}

.markdown-content h3 {
  font-size: var(--font-size-lg);
  margin-top: 1.25rem;
  margin-bottom: 0.75rem;
}

.markdown-content p, .markdown-content ul, .markdown-content ol {
  margin-bottom: 1rem;
}

.markdown-content code:not(pre code) {
  font-family: var(--font-family-mono);
  font-size: 0.9em;
}

/* Responsive Typography */
@media (max-width: 768px) {
  html, body {
    font-size: 15px; /* Slightly smaller base size on tablets */
  }

  h1 {
    font-size: var(--font-size-2xl);
  }

  h2 {
    font-size: var(--font-size-xl);
  }
}

@media (max-width: 640px) {
  html, body {
    font-size: 14px; /* Smaller base size on mobile */
  }

  .markdown-content h1 {
    font-size: var(--font-size-xl);
  }

  .markdown-content h2 {
    font-size: var(--font-size-lg);
  }
}
