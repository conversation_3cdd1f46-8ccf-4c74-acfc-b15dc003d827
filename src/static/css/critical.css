/**
 * 关键路径CSS - 内联到HTML中以避免渲染阻塞
 * 包含首屏渲染必需的最小样式集合
 * 
 * 优化策略：
 * 1. 仅包含首屏可见内容的样式
 * 2. 使用系统字体减少字体加载时间
 * 3. 简化动画和过渡效果
 * 4. 移除未使用的样式规则
 */

/* 基础重置和字体 */
html, body {
    margin: 0;
    padding: 0;
    height: 100%;
    font-family: -apple-system, BlinkMacSystemFont, 'Source Han Sans SC', 'Source Han Sans CN', 'Noto Sans SC', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 'Helvetica Neue', 'Segoe UI', Arial, sans-serif;
    font-size: 16px;
    line-height: 1.5;
    font-weight: 400;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    text-rendering: optimizeLegibility;
}

/* 基础布局 */
#app {
    min-height: 100vh;
    position: relative;
}

/* 背景动画 - 简化版本 */
.bg-gradient-animated {
    background: linear-gradient(-45deg, #ee7752, #e73c7e, #23a6d5, #23d5ab);
    background-size: 400% 400%;
    animation: gradient 15s ease infinite;
}

@keyframes gradient {
    0%, 100% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
}

/* 加载状态 */
.loading-spinner {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid #f3f3f3;
    border-top: 3px solid #3498db;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 基础按钮样式 */
.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 0.5rem 1rem;
    border: none;
    border-radius: 0.375rem;
    font-weight: 500;
    text-decoration: none;
    cursor: pointer;
    transition: all 0.2s ease;
}

.btn-primary {
    background-color: #3b82f6;
    color: white;
}

.btn-primary:hover {
    background-color: #2563eb;
}

/* 基础输入框样式 */
.input {
    width: 100%;
    padding: 0.5rem 0.75rem;
    border: 1px solid #d1d5db;
    border-radius: 0.375rem;
    font-size: 1rem;
    line-height: 1.5;
    transition: border-color 0.2s ease;
}

.input:focus {
    outline: none;
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

/* 基础卡片样式 */
.card {
    background-color: white;
    border-radius: 0.5rem;
    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
    overflow: hidden;
}

/* 响应式设计 - 移动端优先 */
@media (max-width: 640px) {
    html {
        font-size: 14px;
    }
    
    .btn {
        padding: 0.375rem 0.75rem;
        font-size: 0.875rem;
    }
    
    .input {
        padding: 0.375rem 0.5rem;
        font-size: 0.875rem;
    }
}

/* 暗色主题基础支持 */
@media (prefers-color-scheme: dark) {
    html {
        color-scheme: dark;
    }
    
    body {
        background-color: #1f2937;
        color: #f9fafb;
    }
    
    .card {
        background-color: #374151;
        color: #f9fafb;
    }
    
    .input {
        background-color: #374151;
        border-color: #4b5563;
        color: #f9fafb;
    }
    
    .input:focus {
        border-color: #60a5fa;
    }
}

/* 可访问性改进 */
@media (prefers-reduced-motion: reduce) {
    .bg-gradient-animated {
        animation: none;
        background: #23a6d5;
    }
    
    .loading-spinner {
        animation: none;
    }
    
    * {
        transition: none !important;
        animation: none !important;
    }
}

/* 高对比度模式支持 */
@media (prefers-contrast: high) {
    .btn-primary {
        background-color: #000;
        color: #fff;
        border: 2px solid #fff;
    }
    
    .input {
        border-width: 2px;
    }
}

/* 打印样式 */
@media print {
    .bg-gradient-animated {
        background: none !important;
    }
    
    .btn {
        border: 1px solid #000;
        background: none;
        color: #000;
    }
}
