/**
 * 主样式文件 - 性能优化版本
 *
 * 优化策略：
 * 1. 减少@import使用，避免瀑布式加载
 * 2. 仅包含关键路径CSS
 * 3. 其他样式通过JavaScript动态加载
 * 4. 支持渐进式增强
 */

/* 关键路径CSS - 首屏渲染必需 */
/* 基础样式 - 内联最重要的部分 */
:root {
  /* 颜色变量 */
  --color-primary: #3b82f6;
  --color-secondary: #6b7280;
  --color-success: #10b981;
  --color-warning: #f59e0b;
  --color-error: #ef4444;
  --color-text-primary: #111827;
  --color-text-secondary: #6b7280;
  --color-bg-primary: #ffffff;
  --color-bg-secondary: #f9fafb;
  --color-border: #e5e7eb;

  /* 字体变量 */
  --font-family-sans: -apple-system, BlinkMacSystemFont, 'Source Han Sans SC', 'Noto Sans SC', 'PingFang SC', sans-serif;
  --font-size-base: 1rem;
  --line-height-normal: 1.5;

  /* 间距变量 */
  --spacing-xs: 0.25rem;
  --spacing-sm: 0.5rem;
  --spacing-md: 1rem;
  --spacing-lg: 1.5rem;
  --spacing-xl: 2rem;

  /* 动画变量 */
  --duration-fast: 0.15s;
  --duration-normal: 0.3s;
  --duration-slow: 0.5s;
}

/* 暗色主题变量 */
@media (prefers-color-scheme: dark) {
  :root {
    --color-text-primary: #f9fafb;
    --color-text-secondary: #d1d5db;
    --color-bg-primary: #111827;
    --color-bg-secondary: #1f2937;
    --color-border: #374151;
  }
}

/* 基础重置 - 仅关键部分 */
*, *::before, *::after {
  box-sizing: border-box;
}

html, body {
  margin: 0;
  padding: 0;
  height: 100%;
  font-family: var(--font-family-sans);
  font-size: var(--font-size-base);
  line-height: var(--line-height-normal);
  color: var(--color-text-primary);
  background-color: var(--color-bg-primary);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

#app {
  min-height: 100vh;
  position: relative;
}

/* 延迟加载样式的占位符 */
.css-loading {
  opacity: 0.8;
  transition: opacity var(--duration-normal);
}

.css-loaded {
  opacity: 1;
}

/*
 * 其他样式文件将通过JavaScript动态加载
 * 参见: /static/js/utils/CSSLoader.js
 */
