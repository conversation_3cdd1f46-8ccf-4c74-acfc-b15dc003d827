import { createApp } from 'vue';
import DashboardLayout from './dashboard/layouts/DashboardLayout.js';

// 等待Chart.js加载完成后启动应用
async function waitForChartJs() {
    // 如果Chart.js已经加载，直接返回
    if (window.Chart) {
        return Promise.resolve();
    }

    // 等待Chart.js加载，最多等待10秒
    return new Promise((resolve, reject) => {
        let attempts = 0;
        const maxAttempts = 100; // 10秒 (100 * 100ms)

        const checkChart = () => {
            if (window.Chart) {
                console.log('Chart.js已加载完成');
                resolve();
            } else if (attempts >= maxAttempts) {
                console.warn('Chart.js加载超时，继续启动应用');
                resolve(); // 即使超时也继续启动应用
            } else {
                attempts++;
                setTimeout(checkChart, 100);
            }
        };

        checkChart();
    });
}

// 启动Dashboard应用
async function startDashboard() {
    try {
        // 等待Chart.js加载完成
        await waitForChartJs();

        // 创建Vue应用实例
        const app = createApp({
            components: {
                DashboardLayout
            },
            template: `
                <DashboardLayout />
            `
        });

        // 挂载应用到DOM
        app.mount('#app');

        console.log('Dashboard应用已启动');
    } catch (error) {
        console.error('Dashboard启动失败:', error);
    }
}

// 等待DOM加载完成后启动应用
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', startDashboard);
} else {
    startDashboard();
}