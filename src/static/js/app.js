import { createApp } from 'vue';
import { loadMainCSS, loadResponsiveCSS } from './utils/CSSLoader.js';
import { loadOptimizedFonts } from './utils/FontLoader.js';
import { imageOptimizer } from './utils/ImageOptimizer.js';
import { performanceMonitor, markPerformance } from './utils/PerformanceMonitor.js';
import { optimizedImport, modulePreloader } from './utils/ModulePreloader.js';

// 性能优化：延迟加载组件，避免阻塞首屏渲染
let AppLayout, ShareLayout;

// 检查是否是共享会话
const isSharedConversation = window.sharedConversation && window.sharedConversation.isShared;

// 显示加载状态
function showLoadingState() {
    const app = document.getElementById('app');
    app.innerHTML = `
        <div style="
            display: flex;
            justify-content: center;
            align-items: center;
            height: 100vh;
            flex-direction: column;
            gap: 1rem;
        ">
            <div class="loading-spinner"></div>
            <div style="color: var(--color-text-secondary); font-size: 0.875rem;">
                正在加载应用...
            </div>
        </div>
    `;
}

// 异步加载应用组件和样式
async function loadApp() {
    try {
        // 标记应用开始加载
        markPerformance('app-load-start');

        // 启动性能监控
        performanceMonitor.start();

        // 显示加载状态
        showLoadingState();

        // 并行加载CSS、字体、图片优化器和组件
        const [cssResult, layoutComponents] = await Promise.all([
            // 加载CSS样式和字体
            Promise.all([
                loadMainCSS(),
                loadResponsiveCSS(),
                loadOptimizedFonts() // 异步加载优化字体
            ]).catch(error => {
                console.warn('CSS/字体加载失败，使用默认样式:', error);
                return null;
            }),
            // 动态导入组件 - 使用优化的模块加载器
            Promise.all([
                optimizedImport('./chatbi/layouts/ChatbiLayout.js').catch(error => {
                    console.error('ChatbiLayout加载失败:', error);
                    throw error;
                }),
                optimizedImport('./chatbi/layouts/ShareLayout.js').catch(error => {
                    console.error('ShareLayout加载失败:', error);
                    throw error;
                })
            ])
        ]);

        // 解构组件
        [AppLayout, ShareLayout] = layoutComponents.map(module => module.default);

        // 标记CSS加载完成
        markPerformance('css-loaded');
        document.body.classList.add('css-loaded');

        // 标记Vue应用开始挂载
        markPerformance('vue-app-mount-start');

        // 创建Vue应用实例
        const app = createApp({
            components: {
                AppLayout,
                ShareLayout
            },
            setup() {
                return {
                    isSharedConversation
                };
            },
            template: `
                <ShareLayout v-if="isSharedConversation" />
                <AppLayout v-else />
            `
        });

        // 挂载应用到DOM
        app.mount('#app');

        // 标记Vue应用挂载完成
        markPerformance('vue-app-mount-end');

        // 测量Vue应用挂载时间
        if (performance.measure) {
            performance.measure('vue-app-mount', 'vue-app-mount-start', 'vue-app-mount-end');
        }

        // 标记应用加载完成
        markPerformance('app-load-end');

        if (performance.measure) {
            performance.measure('app-load-total', 'app-load-start', 'app-load-end');
        }

    } catch (error) {
        console.error('应用加载失败:', error);

        // 显示错误状态
        const app = document.getElementById('app');
        app.innerHTML = `
            <div style="
                display: flex;
                justify-content: center;
                align-items: center;
                height: 100vh;
                flex-direction: column;
                gap: 1rem;
                color: var(--color-error);
            ">
                <div>⚠️ 应用加载失败</div>
                <div style="font-size: 0.875rem; color: var(--color-text-secondary);">
                    请刷新页面重试
                </div>
            </div>
        `;
    }
}

// 等待DOM加载完成后启动应用
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', loadApp);
} else {
    loadApp();
}
