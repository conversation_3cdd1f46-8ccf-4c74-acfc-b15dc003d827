/**
 * BarCard Component
 *
 * Card component for displaying horizontal bar charts using vue-chartjs
 * Extends BaseDashboardCard with specific styling and functionality for bar charts
 */
import { ref, onMounted, watch, computed, onUnmounted } from 'vue';
import BaseDashboardCard from './BaseDashboardCard.js';

export default {
    name: 'BarCard',
    components: {
        BaseDashboardCard
    },
    props: {
        /**
         * Card title
         */
        title: {
            type: String,
            default: ''
        },
        /**
         * Card subtitle
         */
        subtitle: {
            type: String,
            default: ''
        },
        /**
         * Chart data in Chart.js format
         */
        chartData: {
            type: Object,
            required: true
        },
        /**
         * Bar colors for light and dark modes
         */
        barColors: {
            type: Object,
            default: () => ({
                light: {
                    backgroundColor: 'rgba(59, 130, 246, 0.2)',
                    borderColor: 'rgb(59, 130, 246)',
                },
                dark: {
                    backgroundColor: 'rgba(96, 165, 250, 0.2)',
                    borderColor: 'rgb(96, 165, 250)',
                }
            })
        },
        /**
         * Bar hover colors for light and dark modes
         */
        barHoverColors: {
            type: Object,
            default: () => ({
                light: {
                    backgroundColor: 'rgba(59, 130, 246, 0.4)',
                    borderColor: 'rgb(59, 130, 246)',
                },
                dark: {
                    backgroundColor: 'rgba(96, 165, 250, 0.4)',
                    borderColor: 'rgb(96, 165, 250)',
                }
            })
        },
        /**
         * Whether to enable Chart.js animations
         */
        enableAnimation: {
            type: Boolean,
            default: true
        },
        /**
         * Chart.js options to override defaults
         */
        chartOptions: {
            type: Object,
            default: () => ({})
        },
        /**
         * Whether the card is in a loading state
         */
        loading: {
            type: Boolean,
            default: false
        },
        /**
         * Additional CSS classes for the card
         */
        cardClass: {
            type: String,
            default: ''
        },
        /**
         * Card size (can be 'sm', 'md', 'lg', 'xl' or a Tailwind class like 'row-span-2')
         */
        size: {
            type: String,
            default: 'lg'
        },
        /**
         * Card height (deprecated, use size instead)
         * @deprecated
         */
        height: {
            type: String,
            default: ''
        }
    },
    setup(props) {
        // Reference to the chart canvas element
        const chartCanvas = ref(null);
        // Reference to the Chart.js instance
        const chartInstance = ref(null);
        // Track current theme
        const isDarkTheme = ref(document.documentElement.getAttribute('data-theme') === 'dark');
        // Track if Chart.js is loaded
        const chartJsLoaded = ref(!!window.Chart);

        // Combine classes for the bar card
        const combinedCardClass = computed(() => {
            return `chart-card bar-card ${props.cardClass}`;
        });

        // Initialize and render the chart
        const initChart = () => {
            if (!chartCanvas.value) return;

            // Ensure Chart.js is loaded
            if (!window.Chart) {
                console.error('[BarCard] Chart.js is not loaded');
                chartJsLoaded.value = false;
                return;
            }

            try {
                // Destroy existing chart if it exists
                if (chartInstance.value) {
                    chartInstance.value.destroy();
                }

                // Get the current theme colors
                const themeColors = isDarkTheme.value ? props.barColors.dark : props.barColors.light;
                const hoverColors = isDarkTheme.value ? props.barHoverColors.dark : props.barHoverColors.light;

                // Create a copy of the chart data
                const chartDataCopy = {
                    labels: [...props.chartData.labels || []],
                    datasets: props.chartData.datasets ? props.chartData.datasets.map(dataset => ({
                        ...dataset,
                        backgroundColor: dataset.backgroundColor || themeColors.backgroundColor,
                        borderColor: dataset.borderColor || themeColors.borderColor,
                        borderWidth: dataset.borderWidth || 1,
                        borderRadius: 6,
                        borderSkipped: false,
                        hoverBackgroundColor: dataset.hoverBackgroundColor || hoverColors.backgroundColor,
                        hoverBorderColor: dataset.hoverBorderColor || hoverColors.borderColor,
                        hoverBorderWidth: 2
                    })) : []
                };

                // Default chart options
                const defaultOptions = {
                    indexAxis: 'y',
                    responsive: true,
                    maintainAspectRatio: false,
                    animation: props.enableAnimation
                        ? {
                            duration: 1000,
                            easing: 'easeOutCubic',
                            // Optional: Animate bars progressively
                            // onProgress: function(animation) {
                            //     animation.animationObject.currentStep = animation.numSteps;
                            // }
                          }
                        : { duration: 0 },
                    layout: {
                        padding: {
                            left: 5,
                            right: 20, // Increased padding to prevent labels from being cut off
                            top: 8,
                            bottom: 8
                        }
                    },
                    plugins: {
                        legend: {
                            display: false
                        },
                        tooltip: {
                            enabled: true,
                            mode: 'nearest',
                            intersect: true,
                            backgroundColor: isDarkTheme.value ? 'rgba(30, 30, 30, 0.9)' : 'rgba(255, 255, 255, 0.9)',
                            titleColor: isDarkTheme.value ? '#fff' : '#000',
                            bodyColor: isDarkTheme.value ? '#fff' : '#000',
                            borderColor: isDarkTheme.value ? 'rgba(255, 255, 255, 0.1)' : 'rgba(0, 0, 0, 0.1)',
                            borderWidth: 1,
                            padding: 12,
                            cornerRadius: 12
                        }
                    },
                    scales: {
                        x: {
                            beginAtZero: true,
                            grid: {
                                color: isDarkTheme.value ? 'rgba(255, 255, 255, 0.05)' : 'rgba(0, 0, 0, 0.05)',
                                lineWidth: 0.5
                            },
                            border: {
                                display: false
                            },
                            ticks: {
                                color: isDarkTheme.value ? 'rgba(255, 255, 255, 0.7)' : 'rgba(0, 0, 0, 0.7)',
                                maxRotation: 0,
                                autoSkip: true,
                                autoSkipPadding: 10
                            }
                        },
                        y: {
                            grid: {
                                display: false
                            },
                            border: {
                                display: false
                            },
                            ticks: {
                                color: isDarkTheme.value ? 'rgba(255, 255, 255, 0.85)' : 'rgba(0, 0, 0, 0.8)',
                                font: {
                                    size: 10 // Smaller font size for labels
                                },
                                callback: function(value) {
                                    // Allow labels to wrap to two lines
                                    const label = this.getLabelForValue(value);
                                    if (label && label.length > 12) {
                                        const midpoint = Math.floor(label.length / 2);
                                        // Find a space near the midpoint to split at
                                        let splitPoint = label.lastIndexOf(' ', midpoint);
                                        if (splitPoint === -1 || splitPoint < 3) {
                                            splitPoint = label.indexOf(' ', midpoint);
                                        }
                                        if (splitPoint !== -1 && splitPoint > 3 && splitPoint < label.length - 3) {
                                            return [label.substring(0, splitPoint), label.substring(splitPoint + 1)];
                                        }
                                    }
                                    return label;
                                }
                            }
                        }
                    }
                };

                // Merge options
                const mergedOptions = { ...defaultOptions, ...props.chartOptions };

                // Create the chart
                chartInstance.value = new window.Chart(chartCanvas.value, {
                    type: 'bar',
                    data: chartDataCopy,
                    options: mergedOptions
                });
            } catch (error) {
                console.error('[BarCard] Error initializing chart:', error);
            }
        };

        // Watch for theme changes
        const themeObserver = new MutationObserver((mutationsList) => {
            for (const mutation of mutationsList) {
                if (mutation.type === 'attributes' && mutation.attributeName === 'data-theme') {
                    isDarkTheme.value = document.documentElement.getAttribute('data-theme') === 'dark';
                    if (!props.loading) {
                        initChart();
                    }
                }
            }
        });

        // Watch for changes in chart data
        watch(() => [props.chartData, props.loading], () => {
            if (!props.loading && chartJsLoaded.value) {
                setTimeout(() => {
                    initChart();
                }, 100);
            }
        }, { deep: true });

        // Chart.js加载监听器
        const waitForChartJs = () => {
            if (window.Chart) {
                chartJsLoaded.value = true;
                if (!props.loading) {
                    setTimeout(() => {
                        initChart();
                    }, 100);
                }
                return;
            }

            // 如果Chart.js还没加载，等待一段时间后重试
            let attempts = 0;
            const maxAttempts = 50; // 5秒 (50 * 100ms)

            const checkChart = () => {
                if (window.Chart) {
                    chartJsLoaded.value = true;
                    console.log('[BarCard] Chart.js已加载，初始化图表');
                    if (!props.loading) {
                        setTimeout(() => {
                            initChart();
                        }, 100);
                    }
                } else if (attempts < maxAttempts) {
                    attempts++;
                    setTimeout(checkChart, 100);
                } else {
                    console.warn('[BarCard] Chart.js加载超时，图表将不会显示');
                }
            };

            checkChart();
        };

        // Start observing theme changes
        onMounted(() => {
            chartJsLoaded.value = !!window.Chart;
            if (!chartJsLoaded.value) {
                console.warn('[BarCard] Chart.js not found. Waiting for Chart.js to load...');
                waitForChartJs();
            } else if (!props.loading) {
                setTimeout(() => {
                    initChart();
                }, 100);
            }
            themeObserver.observe(document.documentElement, { attributes: true });
        });

        // Clean up observer and chart instance
        onUnmounted(() => {
            if (chartInstance.value) {
                chartInstance.value.destroy();
            }
            themeObserver.disconnect();
        });



        return {
            chartCanvas,
            chartInstance,
            combinedCardClass,
            chartJsLoaded
        };
    },
    template: `
        <BaseDashboardCard
            :loading="loading"
            :card-class="combinedCardClass"
            :size="size"
        >
            <template #header>
                <div class="flex justify-between items-start w-full">
                    <div class="flex flex-col">
                        <div class="card-title text-base font-medium text-base-content/90 truncate">{{ title }}</div>
                        <div v-if="subtitle" class="card-subtitle text-xs text-base-content/70 mt-0.5">{{ subtitle }}</div>
                    </div>
                </div>
            </template>
            <template #default>
                <div v-if="!loading && !chartJsLoaded" class="flex items-center justify-center h-72 text-center text-error p-4 text-sm">
                    Chart.js library is not loaded. Please ensure it is included in your project.
                </div>
                <div v-else class="chart-container mt-1 h-72">
                    <canvas ref="chartCanvas"></canvas>
                </div>
            </template>
        </BaseDashboardCard>
    `
};
