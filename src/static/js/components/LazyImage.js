/**
 * 懒加载图片组件 - 优化图片加载性能
 * 
 * 功能：
 * 1. 支持图片懒加载，减少初始页面加载时间
 * 2. 提供加载状态和错误处理
 * 3. 支持响应式图片和WebP格式
 * 4. 支持占位符和渐进式加载
 * 
 * 使用方式：
 * <LazyImage 
 *   src="/path/to/image.jpg" 
 *   alt="描述" 
 *   placeholder="/path/to/placeholder.jpg"
 *   webp="/path/to/image.webp"
 * />
 */

import { ref, onMounted, onUnmounted } from 'vue';

export default {
    name: 'LazyImage',
    props: {
        src: {
            type: String,
            required: true
        },
        alt: {
            type: String,
            default: ''
        },
        placeholder: {
            type: String,
            default: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzIwIiBoZWlnaHQ9IjI0MCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSIjZGRkIi8+PHRleHQgeD0iNTAlIiB5PSI1MCUiIGZvbnQtc2l6ZT0iMTgiIHRleHQtYW5jaG9yPSJtaWRkbGUiIGR5PSIuM2VtIiBmaWxsPSIjOTk5Ij5sb2FkaW5nLi4uPC90ZXh0Pjwvc3ZnPg=='
        },
        webp: {
            type: String,
            default: ''
        },
        sizes: {
            type: String,
            default: ''
        },
        srcset: {
            type: String,
            default: ''
        },
        loading: {
            type: String,
            default: 'lazy',
            validator: value => ['lazy', 'eager'].includes(value)
        },
        threshold: {
            type: Number,
            default: 0.1
        },
        rootMargin: {
            type: String,
            default: '50px'
        },
        fadeIn: {
            type: Boolean,
            default: true
        },
        retryCount: {
            type: Number,
            default: 3
        }
    },
    emits: ['load', 'error', 'intersect'],
    setup(props, { emit }) {
        const imageRef = ref(null);
        const isLoaded = ref(false);
        const isError = ref(false);
        const isIntersecting = ref(false);
        const currentSrc = ref(props.placeholder);
        const retries = ref(0);
        
        let observer = null;

        // 检查WebP支持
        const supportsWebP = ref(false);
        const checkWebPSupport = () => {
            const canvas = document.createElement('canvas');
            canvas.width = 1;
            canvas.height = 1;
            const ctx = canvas.getContext('2d');
            ctx.fillStyle = 'rgba(0,0,0,0)';
            ctx.fillRect(0, 0, 1, 1);
            supportsWebP.value = canvas.toDataURL('image/webp').indexOf('data:image/webp') === 0;
        };

        // 获取最佳图片源
        const getBestImageSrc = () => {
            if (props.webp && supportsWebP.value) {
                return props.webp;
            }
            return props.src;
        };

        // 加载图片
        const loadImage = async () => {
            if (isLoaded.value || isError.value) return;

            const img = new Image();
            const src = getBestImageSrc();

            return new Promise((resolve, reject) => {
                img.onload = () => {
                    currentSrc.value = src;
                    isLoaded.value = true;
                    isError.value = false;
                    emit('load', img);
                    resolve(img);
                };

                img.onerror = () => {
                    if (retries.value < props.retryCount) {
                        retries.value++;
                        console.warn(`图片加载失败，重试 ${retries.value}/${props.retryCount}: ${src}`);
                        setTimeout(() => loadImage(), Math.pow(2, retries.value) * 1000);
                    } else {
                        isError.value = true;
                        emit('error', new Error(`Failed to load image: ${src}`));
                        reject(new Error(`Failed to load image: ${src}`));
                    }
                };

                // 设置图片属性
                if (props.srcset) {
                    img.srcset = props.srcset;
                }
                if (props.sizes) {
                    img.sizes = props.sizes;
                }
                
                img.src = src;
            });
        };

        // 创建Intersection Observer
        const createObserver = () => {
            if (!window.IntersectionObserver) {
                // 不支持IntersectionObserver，直接加载
                loadImage();
                return;
            }

            observer = new IntersectionObserver(
                (entries) => {
                    entries.forEach((entry) => {
                        if (entry.isIntersecting) {
                            isIntersecting.value = true;
                            emit('intersect', entry);
                            loadImage();
                            observer.unobserve(entry.target);
                        }
                    });
                },
                {
                    threshold: props.threshold,
                    rootMargin: props.rootMargin
                }
            );

            if (imageRef.value) {
                observer.observe(imageRef.value);
            }
        };

        // 组件挂载
        onMounted(() => {
            checkWebPSupport();
            
            if (props.loading === 'eager') {
                loadImage();
            } else {
                createObserver();
            }
        });

        // 组件卸载
        onUnmounted(() => {
            if (observer) {
                observer.disconnect();
            }
        });

        return {
            imageRef,
            isLoaded,
            isError,
            isIntersecting,
            currentSrc,
            supportsWebP
        };
    },
    template: `
        <div 
            ref="imageRef"
            class="lazy-image-container"
            :class="{
                'lazy-image-loaded': isLoaded,
                'lazy-image-error': isError,
                'lazy-image-fade': fadeIn
            }"
        >
            <img
                :src="currentSrc"
                :alt="alt"
                :loading="loading"
                class="lazy-image"
                :class="{
                    'lazy-image-visible': isLoaded,
                    'lazy-image-hidden': !isLoaded && !isError
                }"
            />
            
            <!-- 错误状态 -->
            <div v-if="isError" class="lazy-image-error-placeholder">
                <div class="error-icon">📷</div>
                <div class="error-text">图片加载失败</div>
            </div>
            
            <!-- 加载状态 -->
            <div v-if="!isLoaded && !isError" class="lazy-image-loading">
                <div class="loading-spinner"></div>
            </div>
        </div>
    `,
    style: `
        .lazy-image-container {
            position: relative;
            display: inline-block;
            overflow: hidden;
            background-color: #f5f5f5;
        }

        .lazy-image {
            width: 100%;
            height: auto;
            display: block;
            transition: opacity 0.3s ease;
        }

        .lazy-image-fade .lazy-image-visible {
            opacity: 1;
        }

        .lazy-image-fade .lazy-image-hidden {
            opacity: 0;
        }

        .lazy-image-loading {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .lazy-image-error-placeholder {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            gap: 0.5rem;
            color: #666;
            font-size: 0.875rem;
        }

        .error-icon {
            font-size: 2rem;
            opacity: 0.5;
        }

        .error-text {
            text-align: center;
        }

        /* 响应式设计 */
        @media (max-width: 640px) {
            .lazy-image-container {
                max-width: 100%;
            }
        }

        /* 暗色主题支持 */
        @media (prefers-color-scheme: dark) {
            .lazy-image-container {
                background-color: #374151;
            }
            
            .lazy-image-error-placeholder {
                color: #d1d5db;
            }
        }

        /* 高对比度模式 */
        @media (prefers-contrast: high) {
            .lazy-image-container {
                border: 1px solid #000;
            }
        }

        /* 减少动画模式 */
        @media (prefers-reduced-motion: reduce) {
            .lazy-image {
                transition: none;
            }
        }
    `
};
