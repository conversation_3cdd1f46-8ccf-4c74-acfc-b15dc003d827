/**
 * Markdown rendering utility
 *
 * 使用最新版本的 marked.js (v11+) 和 marked-highlight 扩展
 * 配置 marked.js 与 highlight.js 集成
 * 自定义代码块和表格渲染
 */

// 安全地配置hljs，避免在hljs未加载时出错
if (window.hljs && typeof window.hljs.configure === 'function') {
    window.hljs.configure({ ignoreUnescapedHTML: true });
}

// 初始化 marked 配置
export function initializeMarked() {
    if (!window.marked || !window.markedHighlight) {
        console.warn('Marked.js 或 marked-highlight 未加载，跳过初始化');
        return;
    }

    // 确保hljs已加载
    if (!window.hljs || typeof window.hljs.configure !== 'function') {
        console.warn('highlight.js 未加载，跳过语法高亮配置');
        return;
    }

    // 创建 marked 实例
    const markedInstance = new window.marked.Marked(
        // 使用 marked-highlight 扩展
        window.markedHighlight.markedHighlight({
            langPrefix: 'language-',
            emptyLangClass: 'hljs',
            highlight(code, lang) {
                try {
                    // 确保code是字符串
                    if (typeof code !== 'string') {
                        code = String(code || '');
                    }

                    if (lang && window.hljs.getLanguage(lang)) {
                        return window.hljs.highlight(code, { language: lang }).value;
                    }
                    return window.hljs.highlightAuto(code).value;
                } catch (e) {
                    console.error('高亮代码时出错:', e);
                    // 确保code是字符串后再使用replace
                    const safeCode = typeof code === 'string' ? code : String(code || '');
                    return safeCode
                        .replace(/&/g, '&amp;')
                        .replace(/</g, '&lt;')
                        .replace(/>/g, '&gt;');
                }
            }
        })
    );

    // 自定义渲染器
    const renderer = {
        // 自定义表格渲染
        table(header, body) {
            return `<div class="table-container my-4 rounded-lg">
                <table class="table table-zebra w-full" style="transition: border-color 250ms, background-color 250ms;">
                    <thead>${header}</thead>
                    <tbody>${body}</tbody>
                </table>
            </div>`;
        },

        // 自定义代码块渲染
        code(code, language) {
            // 确保code是字符串
            if (typeof code !== 'string') {
                code = String(code || '');
            }

            const displayLang = language ? language.toUpperCase() : 'CODE';
            const codeBlockId = `code-block-${Math.random().toString(36).substring(2, 10)}`;
            const langClass = language ? `language-${language}` : '';

            // 如果代码为空，显示一个空代码块
            if (!code.trim()) {
                code = ' '; // 至少有一个空格，避免空元素渲染问题
            }

            // 返回带有工具栏的代码块
            return `<div class="hljs-container" id="${codeBlockId}">
                <div class="hljs-content">
                    <div class="hljs-toolbar">
                        <span class="hljs-language">${displayLang}</span>
                        <button class="btn btn-xs btn-ghost hljs-copy-button" onclick="copyCodeToClipboard(this, '${codeBlockId}')">
                            复制
                        </button>
                    </div>
                    <pre><code class="${langClass}" data-highlighted="no">${code}</code></pre>
                </div>
            </div>`;
        }
    };

    // 设置 marked 选项 - 添加性能优化
    markedInstance.use({
        renderer,
        gfm: true,
        breaks: true,
        // 添加性能优化选项
        silent: true,           // 忽略错误
        smartypants: false,     // 禁用智能标点转换
        headerIds: false,       // 禁用标题ID生成
    });

    // 将实例挂载到全局，以便在其他地方使用
    window.markedInstance = markedInstance;
}

// 简单的缓存机制，避免重复渲染相同的内容
const markdownCache = new Map();
const MAX_CACHE_SIZE = 50; // 最大缓存条目数

// 渲染Markdown为HTML - 添加缓存和性能优化
export function renderMarkdown(text) {
    try {
        // 确保text是字符串
        if (typeof text !== 'string') {
            text = String(text);
        }

        // 对于非常短的文本，不使用缓存，直接渲染
        if (text.length < 10) {
            return renderMarkdownInternal(text);
        }

        // 检查缓存
        if (markdownCache.has(text)) {
            return markdownCache.get(text);
        }

        // 渲染并缓存结果
        const html = renderMarkdownInternal(text);

        // 如果缓存太大，删除最早的条目
        if (markdownCache.size >= MAX_CACHE_SIZE) {
            const oldestKey = markdownCache.keys().next().value;
            markdownCache.delete(oldestKey);
        }

        // 添加到缓存
        markdownCache.set(text, html);

        return html;
    } catch (error) {
        console.error('Markdown渲染错误:', error);
        // 出错时返回简单转义的文本
        return text
            .replace(/</g, '&lt;')
            .replace(/>/g, '&gt;')
            .replace(/\n/g, '<br>');
    }
}

// 内部渲染函数
function renderMarkdownInternal(text) {
    // 检查是否已初始化
    if (!window.markedInstance) {
        // 如果没有初始化，尝试初始化
        if (window.marked) {
            initializeMarked();
        } else {
            throw new Error('Marked.js 未加载');
        }
    }

    // 使用 marked 实例解析 Markdown
    return window.markedInstance.parse(text);
}
