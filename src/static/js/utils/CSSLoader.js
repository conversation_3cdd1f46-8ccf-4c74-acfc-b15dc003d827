/**
 * CSS加载器 - 优化CSS资源加载策略
 * 
 * 功能：
 * 1. 并行加载CSS文件，避免@import的瀑布式加载
 * 2. 支持关键CSS优先加载
 * 3. 支持媒体查询条件加载
 * 4. 提供加载状态回调
 * 
 * 使用方式：
 * import { loadCSS, loadCSSBundle } from './CSSLoader.js';
 * loadCSS('/path/to/style.css');
 * loadCSSBundle(['style1.css', 'style2.css']);
 */

/**
 * 加载单个CSS文件
 * @param {string} href - CSS文件路径
 * @param {Object} options - 加载选项
 * @param {string} options.media - 媒体查询条件
 * @param {boolean} options.critical - 是否为关键CSS
 * @param {Function} options.onLoad - 加载完成回调
 * @param {Function} options.onError - 加载失败回调
 * @returns {Promise<void>}
 */
export function loadCSS(href, options = {}) {
    return new Promise((resolve, reject) => {
        // 检查是否已经加载
        const existingLink = document.querySelector(`link[href="${href}"]`);
        if (existingLink) {
            resolve();
            return;
        }

        const link = document.createElement('link');
        link.rel = 'stylesheet';
        link.href = href;
        
        if (options.media) {
            link.media = options.media;
        }

        // 设置加载完成回调
        link.onload = () => {
            if (options.onLoad) options.onLoad();
            resolve();
        };

        // 设置加载失败回调
        link.onerror = () => {
            console.warn(`CSS加载失败: ${href}`);
            if (options.onError) options.onError();
            reject(new Error(`Failed to load CSS: ${href}`));
        };

        // 根据是否为关键CSS决定插入位置
        if (options.critical) {
            document.head.insertBefore(link, document.head.firstChild);
        } else {
            document.head.appendChild(link);
        }
    });
}

/**
 * 并行加载多个CSS文件
 * @param {Array<string|Object>} cssFiles - CSS文件列表
 * @param {Object} globalOptions - 全局选项
 * @returns {Promise<void[]>}
 */
export function loadCSSBundle(cssFiles, globalOptions = {}) {
    const loadPromises = cssFiles.map(file => {
        if (typeof file === 'string') {
            return loadCSS(file, globalOptions);
        } else {
            return loadCSS(file.href, { ...globalOptions, ...file.options });
        }
    });

    return Promise.all(loadPromises);
}

/**
 * 延迟加载CSS文件
 * @param {string} href - CSS文件路径
 * @param {number} delay - 延迟时间（毫秒）
 * @param {Object} options - 加载选项
 * @returns {Promise<void>}
 */
export function loadCSSDeferred(href, delay = 0, options = {}) {
    return new Promise((resolve, reject) => {
        setTimeout(() => {
            loadCSS(href, options).then(resolve).catch(reject);
        }, delay);
    });
}

/**
 * 条件加载CSS文件
 * @param {string} href - CSS文件路径
 * @param {Function|boolean} condition - 加载条件
 * @param {Object} options - 加载选项
 * @returns {Promise<void>}
 */
export function loadCSSConditional(href, condition, options = {}) {
    return new Promise((resolve, reject) => {
        const shouldLoad = typeof condition === 'function' ? condition() : condition;
        
        if (shouldLoad) {
            loadCSS(href, options).then(resolve).catch(reject);
        } else {
            resolve();
        }
    });
}

/**
 * 预加载CSS文件（不应用样式）
 * @param {string} href - CSS文件路径
 * @returns {Promise<void>}
 */
export function preloadCSS(href) {
    return new Promise((resolve, reject) => {
        const link = document.createElement('link');
        link.rel = 'preload';
        link.as = 'style';
        link.href = href;
        
        link.onload = resolve;
        link.onerror = reject;
        
        document.head.appendChild(link);
    });
}

/**
 * 获取CSS文件加载状态
 * @param {string} href - CSS文件路径
 * @returns {string} - 'loaded', 'loading', 'not-loaded'
 */
export function getCSSLoadStatus(href) {
    const link = document.querySelector(`link[href="${href}"]`);
    if (!link) return 'not-loaded';
    
    // 检查是否已加载完成
    if (link.sheet && link.sheet.cssRules) {
        return 'loaded';
    }
    
    return 'loading';
}

/**
 * 移除CSS文件
 * @param {string} href - CSS文件路径
 */
export function removeCSS(href) {
    const link = document.querySelector(`link[href="${href}"]`);
    if (link) {
        link.remove();
    }
}

/**
 * 优化的CSS加载策略 - 替代main.css中的@import
 */
export function loadMainCSS() {
    // 获取应用根路径
    const appRoot = window.userInfo?.appRoot || '';

    // 定义CSS文件加载顺序和优先级
    const cssFiles = [
        // 基础样式 - 高优先级
        { href: `${appRoot}/static/css/base/theme.css`, options: { critical: true } },
        { href: `${appRoot}/static/css/base/font.css`, options: { critical: true } },
        { href: `${appRoot}/static/css/base/scrollbar.css`, options: { critical: true } },
        { href: `${appRoot}/static/css/base/animations.css`, options: {} },

        // 布局样式 - 中优先级
        { href: `${appRoot}/static/css/layouts/sidebar-layout.css`, options: {} },
        { href: `${appRoot}/static/css/layouts/drawer-layout.css`, options: {} },
        { href: `${appRoot}/static/css/layouts/content-layout.css`, options: {} },

        // 通用组件样式
        { href: `${appRoot}/static/css/components/common/ui.css`, options: {} },
        { href: `${appRoot}/static/css/components/common/buttons.css`, options: {} },
        { href: `${appRoot}/static/css/components/common/dropdown.css`, options: {} },
        { href: `${appRoot}/static/css/components/common/modal.css`, options: {} },

        // 聊天界面组件样式 - 延迟加载
        { href: `${appRoot}/static/css/components/chatbi/chat-layout.css`, options: {} },
        { href: `${appRoot}/static/css/components/chatbi/chat-bubble.css`, options: {} },
        { href: `${appRoot}/static/css/components/chatbi/message.css`, options: {} },
        { href: `${appRoot}/static/css/components/chatbi/chat-input.css`, options: {} },
        { href: `${appRoot}/static/css/components/chatbi/welcome.css`, options: {} }
    ];

    // 分批加载，避免同时发起过多请求
    const batchSize = 4;
    const batches = [];

    for (let i = 0; i < cssFiles.length; i += batchSize) {
        batches.push(cssFiles.slice(i, i + batchSize));
    }

    // 依次加载每个批次，但允许一定的并行度
    return batches.reduce((promise, batch, index) => {
        return promise.then(() => {
            // 关键样式立即加载，其他样式稍微延迟
            const delay = index === 0 ? 0 : index * 50;
            return new Promise(resolve => {
                setTimeout(() => {
                    loadCSSBundle(batch).then(resolve).catch(() => {
                        console.warn(`CSS批次 ${index + 1} 加载失败，继续加载下一批次`);
                        resolve();
                    });
                }, delay);
            });
        });
    }, Promise.resolve());
}

/**
 * 响应式CSS加载 - 根据屏幕尺寸加载不同样式
 */
export function loadResponsiveCSS() {
    const appRoot = window.userInfo?.appRoot || '';
    const isMobile = window.innerWidth <= 768;
    const isTablet = window.innerWidth > 768 && window.innerWidth <= 1024;

    // 加载剩余的组件样式，根据设备类型优化加载顺序
    const componentFiles = [];

    if (isMobile) {
        // 移动端优先加载移动相关组件
        componentFiles.push(
            `${appRoot}/static/css/components/common/markdown.css`,
            `${appRoot}/static/css/components/common/code-block.css`,
            `${appRoot}/static/css/components/chatbi/template-buttons.css`,
            `${appRoot}/static/css/components/chatbi/question-prompts.css`
        );
    } else {
        // 桌面端加载完整组件集
        componentFiles.push(
            `${appRoot}/static/css/components/common/markdown.css`,
            `${appRoot}/static/css/components/common/code-block.css`,
            `${appRoot}/static/css/components/chatbi/template-buttons.css`,
            `${appRoot}/static/css/components/chatbi/question-prompts.css`,
            `${appRoot}/static/css/components/chatbi/welcome-title.css`,
            `${appRoot}/static/css/components/chatbi/conversation-list.css`,
            `${appRoot}/static/css/components/chatbi/dev-log-panel.css`,
            `${appRoot}/static/css/components/dashboard/dashboard-card.css`,
            `${appRoot}/static/css/components/dashboard/dashboard-header.css`,
            `${appRoot}/static/css/components/dashboard/conversation-list.css`,
            `${appRoot}/static/css/components/dashboard/dark-mode.css`,
            `${appRoot}/static/css/components/dashboard/conversation-list-fixes.css`,
            `${appRoot}/static/css/components/dashboard/agent-tags.css`
        );
    }

    return loadCSSBundle(componentFiles);
}

/**
 * 主题相关CSS加载
 * @param {string} theme - 主题名称 ('light' | 'dark')
 */
export function loadThemeCSS(theme = 'light') {
    const themeFiles = [
        `/static/css/themes/${theme}.css`,
        `/static/css/components/common/theme-${theme}.css`
    ];
    
    return loadCSSBundle(themeFiles);
}
