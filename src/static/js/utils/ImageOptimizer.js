/**
 * 图片优化工具 - 提升图片加载性能
 *
 * 功能：
 * 1. 自动选择最佳图片格式（WebP、AVIF等）
 * 2. 响应式图片处理
 * 3. 图片压缩和尺寸优化
 * 4. 图片预加载和懒加载
 * 5. 图片缓存策略
 *
 * 使用方式：
 * import { optimizeImage, generateSrcSet } from './ImageOptimizer.js';
 * const optimizedSrc = optimizeImage('/path/to/image.jpg', { width: 800, quality: 80 });
 */

/**
 * 支持的图片格式检测
 */
class FormatDetector {
    constructor() {
        this.supportCache = new Map();
        this.detectFormats();
    }

    /**
     * 检测浏览器支持的图片格式
     */
    async detectFormats() {
        const formats = ['webp', 'avif', 'jpeg-xl'];

        for (const format of formats) {
            try {
                const supported = await this.checkFormatSupport(format);
                this.supportCache.set(format, supported);
            } catch (error) {
                this.supportCache.set(format, false);
            }
        }
    }

    /**
     * 检查特定格式支持
     */
    checkFormatSupport(format) {
        return new Promise((resolve) => {
            const img = new Image();

            img.onload = () => resolve(true);
            img.onerror = () => resolve(false);

            // 测试图片数据
            const testImages = {
                webp: 'data:image/webp;base64,UklGRiIAAABXRUJQVlA4IBYAAAAwAQCdASoBAAEADsD+JaQAA3AAAAAA',
                avif: 'data:image/avif;base64,AAAAIGZ0eXBhdmlmAAAAAGF2aWZtaWYxbWlhZk1BMUIAAADybWV0YQAAAAAAAAAoaGRscgAAAAAAAAAAcGljdAAAAAAAAAAAAAAAAGxpYmF2aWYAAAAADnBpdG0AAAAAAAEAAAAeaWxvYwAAAABEAAABAAEAAAABAAABGgAAAB0AAAAoaWluZgAAAAAAAQAAABppbmZlAgAAAAABAABhdjAxQ29sb3IAAAAAamlwcnAAAABLaXBjbwAAABRpc3BlAAAAAAAAAAIAAAACAAAAEHBpeGkAAAAAAwgICAAAAAxhdjFDgQ0MAAAAABNjb2xybmNseAACAAIAAYAAAAAXaXBtYQAAAAAAAAABAAEEAQKDBAAAACVtZGF0EgAKCBgABogQEAwgMg8f8D///8WfhwB8+ErK42A=',
                'jpeg-xl': 'data:image/jxl;base64,/woIELASCAgQAFwASxLFihQAAAAA'
            };

            img.src = testImages[format] || '';
        });
    }

    /**
     * 获取最佳支持的格式
     */
    getBestFormat(originalFormat = 'jpeg') {
        if (this.supportCache.get('avif')) return 'avif';
        if (this.supportCache.get('webp')) return 'webp';
        if (this.supportCache.get('jpeg-xl')) return 'jxl';
        return originalFormat;
    }

    /**
     * 检查格式是否支持
     */
    isFormatSupported(format) {
        return this.supportCache.get(format) || false;
    }
}

// 全局格式检测器
const formatDetector = new FormatDetector();

/**
 * 图片优化器
 */
export class ImageOptimizer {
    constructor(options = {}) {
        this.baseUrl = options.baseUrl || '';
        this.defaultQuality = options.quality || 80;
        this.defaultFormat = options.format || 'auto';
        this.cache = new Map();
    }

    /**
     * 优化图片URL
     * @param {string} src - 原始图片URL
     * @param {Object} options - 优化选项
     * @returns {string} 优化后的图片URL
     */
    optimizeImage(src, options = {}) {
        const {
            width,
            height,
            quality = this.defaultQuality,
            format = this.defaultFormat,
            dpr = window.devicePixelRatio || 1
        } = options;

        // 生成缓存键
        const cacheKey = `${src}-${width}-${height}-${quality}-${format}-${dpr}`;

        if (this.cache.has(cacheKey)) {
            return this.cache.get(cacheKey);
        }

        let optimizedSrc = src;

        // 如果是相对路径，添加基础URL
        if (!src.startsWith('http') && !src.startsWith('data:')) {
            optimizedSrc = `${this.baseUrl}${src}`;
        }

        // 构建优化参数
        const params = new URLSearchParams();

        if (width) params.set('w', Math.round(width * dpr));
        if (height) params.set('h', Math.round(height * dpr));
        if (quality !== 80) params.set('q', quality);

        // 自动格式选择
        if (format === 'auto') {
            const bestFormat = formatDetector.getBestFormat();
            if (bestFormat !== 'jpeg') {
                params.set('f', bestFormat);
            }
        } else if (format !== 'original') {
            params.set('f', format);
        }

        // 添加参数到URL
        if (params.toString()) {
            const separator = optimizedSrc.includes('?') ? '&' : '?';
            optimizedSrc = `${optimizedSrc}${separator}${params.toString()}`;
        }

        this.cache.set(cacheKey, optimizedSrc);
        return optimizedSrc;
    }

    /**
     * 生成响应式图片srcset
     * @param {string} src - 原始图片URL
     * @param {Array} breakpoints - 断点配置
     * @returns {string} srcset字符串
     */
    generateSrcSet(src, breakpoints = []) {
        const defaultBreakpoints = [
            { width: 320, descriptor: '320w' },
            { width: 640, descriptor: '640w' },
            { width: 768, descriptor: '768w' },
            { width: 1024, descriptor: '1024w' },
            { width: 1280, descriptor: '1280w' },
            { width: 1920, descriptor: '1920w' }
        ];

        const points = breakpoints.length > 0 ? breakpoints : defaultBreakpoints;

        return points.map(point => {
            const optimizedSrc = this.optimizeImage(src, { width: point.width });
            return `${optimizedSrc} ${point.descriptor}`;
        }).join(', ');
    }

    /**
     * 生成sizes属性
     * @param {Array} sizeRules - 尺寸规则
     * @returns {string} sizes字符串
     */
    generateSizes(sizeRules = []) {
        const defaultRules = [
            '(max-width: 320px) 280px',
            '(max-width: 640px) 600px',
            '(max-width: 768px) 728px',
            '(max-width: 1024px) 984px',
            '1200px'
        ];

        const rules = sizeRules.length > 0 ? sizeRules : defaultRules;
        return rules.join(', ');
    }

    /**
     * 预加载图片
     * @param {string} src - 图片URL
     * @param {Object} options - 预加载选项
     * @returns {Promise<HTMLImageElement>}
     */
    preloadImage(src, options = {}) {
        return new Promise((resolve, reject) => {
            const img = new Image();

            img.onload = () => resolve(img);
            img.onerror = reject;

            // 设置crossOrigin以支持CORS
            if (options.crossOrigin) {
                img.crossOrigin = options.crossOrigin;
            }

            // 设置srcset和sizes
            if (options.srcset) {
                img.srcset = options.srcset;
            }
            if (options.sizes) {
                img.sizes = options.sizes;
            }

            img.src = src;
        });
    }

    /**
     * 批量预加载图片
     * @param {Array} images - 图片配置数组
     * @returns {Promise<Array>}
     */
    preloadImages(images) {
        const loadPromises = images.map(img => {
            if (typeof img === 'string') {
                return this.preloadImage(img);
            } else {
                return this.preloadImage(img.src, img.options);
            }
        });

        return Promise.allSettled(loadPromises);
    }

    /**
     * 清除缓存
     */
    clearCache() {
        this.cache.clear();
    }
}

// 默认图片优化器实例
export const imageOptimizer = new ImageOptimizer({
    baseUrl: window.userInfo?.appRoot || '',
    quality: 85
});

/**
 * 便捷函数：优化图片
 */
export function optimizeImage(src, options = {}) {
    return imageOptimizer.optimizeImage(src, options);
}

/**
 * 便捷函数：生成srcset
 */
export function generateSrcSet(src, breakpoints) {
    return imageOptimizer.generateSrcSet(src, breakpoints);
}

/**
 * 便捷函数：预加载图片
 */
export function preloadImage(src, options) {
    return imageOptimizer.preloadImage(src, options);
}

/**
 * 便捷函数：预加载多张图片
 */
export function preloadImages(images) {
    return imageOptimizer.preloadImages(images);
}

/**
 * 获取设备像素比优化的尺寸
 */
export function getOptimizedDimensions(width, height) {
    const dpr = Math.min(window.devicePixelRatio || 1, 2); // 限制最大DPR为2
    return {
        width: Math.round(width * dpr),
        height: height ? Math.round(height * dpr) : undefined
    };
}

/**
 * 检测图片格式支持
 */
export function checkImageFormatSupport(format) {
    return formatDetector.isFormatSupported(format);
}