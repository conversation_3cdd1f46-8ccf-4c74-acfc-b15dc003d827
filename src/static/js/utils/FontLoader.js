/**
 * 字体加载优化器 - 提升字体加载性能
 * 
 * 功能：
 * 1. 支持字体预加载和延迟加载
 * 2. 字体显示策略优化（font-display）
 * 3. 字体子集化和格式优化
 * 4. 字体加载状态管理
 * 5. 系统字体回退策略
 * 
 * 使用方式：
 * import { loadFont, preloadFonts } from './FontLoader.js';
 * loadFont('Source Han Sans SC', '/fonts/source-han-sans-sc.woff2');
 */

/**
 * 字体加载状态枚举
 */
export const FontLoadStatus = {
    NOT_LOADED: 'not-loaded',
    LOADING: 'loading',
    LOADED: 'loaded',
    ERROR: 'error'
};

/**
 * 字体加载管理器
 */
class FontLoadManager {
    constructor() {
        this.loadedFonts = new Map();
        this.loadingPromises = new Map();
        this.fontFaceObserver = null;
        this.initFontFaceObserver();
    }

    /**
     * 初始化FontFace Observer（如果支持）
     */
    initFontFaceObserver() {
        if ('FontFace' in window) {
            this.fontFaceObserver = true;
        }
    }

    /**
     * 加载单个字体
     * @param {string} fontFamily - 字体族名称
     * @param {string} fontUrl - 字体文件URL
     * @param {Object} options - 加载选项
     * @returns {Promise<void>}
     */
    async loadFont(fontFamily, fontUrl, options = {}) {
        const {
            fontWeight = 'normal',
            fontStyle = 'normal',
            fontDisplay = 'swap',
            timeout = 10000,
            fallback = true
        } = options;

        const fontKey = `${fontFamily}-${fontWeight}-${fontStyle}`;

        // 检查是否已经加载
        if (this.loadedFonts.get(fontKey) === FontLoadStatus.LOADED) {
            return Promise.resolve();
        }

        // 检查是否正在加载
        if (this.loadingPromises.has(fontKey)) {
            return this.loadingPromises.get(fontKey);
        }

        // 标记为加载中
        this.loadedFonts.set(fontKey, FontLoadStatus.LOADING);

        const loadPromise = this.performFontLoad(fontFamily, fontUrl, {
            fontWeight,
            fontStyle,
            fontDisplay,
            timeout,
            fallback
        });

        this.loadingPromises.set(fontKey, loadPromise);

        try {
            await loadPromise;
            this.loadedFonts.set(fontKey, FontLoadStatus.LOADED);
            console.log(`字体加载成功: ${fontFamily}`);
        } catch (error) {
            this.loadedFonts.set(fontKey, FontLoadStatus.ERROR);
            console.warn(`字体加载失败: ${fontFamily}`, error);
            
            if (fallback) {
                this.applyFallbackFont(fontFamily);
            }
        } finally {
            this.loadingPromises.delete(fontKey);
        }

        return loadPromise;
    }

    /**
     * 执行字体加载
     */
    async performFontLoad(fontFamily, fontUrl, options) {
        if (this.fontFaceObserver && 'FontFace' in window) {
            return this.loadWithFontFace(fontFamily, fontUrl, options);
        } else {
            return this.loadWithCSS(fontFamily, fontUrl, options);
        }
    }

    /**
     * 使用FontFace API加载字体
     */
    async loadWithFontFace(fontFamily, fontUrl, options) {
        const { fontWeight, fontStyle, fontDisplay, timeout } = options;

        const fontFace = new FontFace(fontFamily, `url(${fontUrl})`, {
            weight: fontWeight,
            style: fontStyle,
            display: fontDisplay
        });

        // 设置超时
        const timeoutPromise = new Promise((_, reject) => {
            setTimeout(() => reject(new Error('Font load timeout')), timeout);
        });

        try {
            const loadedFont = await Promise.race([fontFace.load(), timeoutPromise]);
            document.fonts.add(loadedFont);
            return loadedFont;
        } catch (error) {
            throw new Error(`FontFace load failed: ${error.message}`);
        }
    }

    /**
     * 使用CSS加载字体
     */
    async loadWithCSS(fontFamily, fontUrl, options) {
        const { fontWeight, fontStyle, fontDisplay, timeout } = options;

        return new Promise((resolve, reject) => {
            const style = document.createElement('style');
            style.textContent = `
                @font-face {
                    font-family: '${fontFamily}';
                    src: url('${fontUrl}') format('woff2');
                    font-weight: ${fontWeight};
                    font-style: ${fontStyle};
                    font-display: ${fontDisplay};
                }
            `;

            document.head.appendChild(style);

            // 创建测试元素检测字体是否加载完成
            const testElement = document.createElement('div');
            testElement.style.fontFamily = `'${fontFamily}', monospace`;
            testElement.style.position = 'absolute';
            testElement.style.left = '-9999px';
            testElement.style.fontSize = '72px';
            testElement.textContent = 'BESbswy';
            document.body.appendChild(testElement);

            const initialWidth = testElement.offsetWidth;
            let attempts = 0;
            const maxAttempts = timeout / 100;

            const checkFont = () => {
                attempts++;
                const currentWidth = testElement.offsetWidth;

                if (currentWidth !== initialWidth) {
                    // 字体已加载
                    document.body.removeChild(testElement);
                    resolve();
                } else if (attempts >= maxAttempts) {
                    // 超时
                    document.body.removeChild(testElement);
                    reject(new Error('Font load timeout'));
                } else {
                    // 继续检查
                    setTimeout(checkFont, 100);
                }
            };

            setTimeout(checkFont, 100);
        });
    }

    /**
     * 应用回退字体
     */
    applyFallbackFont(fontFamily) {
        const fallbackFonts = {
            'Source Han Sans SC': '-apple-system, BlinkMacSystemFont, "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", sans-serif',
            'Source Han Serif SC': '"STSong", "SimSun", serif',
            'JetBrains Mono': '"SF Mono", "Menlo", "Consolas", monospace'
        };

        const fallback = fallbackFonts[fontFamily];
        if (fallback) {
            const style = document.createElement('style');
            style.textContent = `
                .font-${fontFamily.toLowerCase().replace(/\s+/g, '-')} {
                    font-family: ${fallback} !important;
                }
            `;
            document.head.appendChild(style);
        }
    }

    /**
     * 获取字体加载状态
     */
    getFontStatus(fontFamily, fontWeight = 'normal', fontStyle = 'normal') {
        const fontKey = `${fontFamily}-${fontWeight}-${fontStyle}`;
        return this.loadedFonts.get(fontKey) || FontLoadStatus.NOT_LOADED;
    }

    /**
     * 批量预加载字体
     */
    async preloadFonts(fonts) {
        const loadPromises = fonts.map(font => {
            if (typeof font === 'string') {
                // 简单格式：只有URL
                const fontFamily = this.extractFontFamilyFromUrl(font);
                return this.loadFont(fontFamily, font);
            } else {
                // 完整格式：包含配置
                return this.loadFont(font.family, font.url, font.options);
            }
        });

        return Promise.allSettled(loadPromises);
    }

    /**
     * 从URL提取字体族名称
     */
    extractFontFamilyFromUrl(url) {
        const filename = url.split('/').pop().split('.')[0];
        return filename.replace(/[-_]/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
    }
}

// 全局字体加载管理器实例
const fontManager = new FontLoadManager();

/**
 * 加载字体（公共API）
 */
export function loadFont(fontFamily, fontUrl, options = {}) {
    return fontManager.loadFont(fontFamily, fontUrl, options);
}

/**
 * 预加载字体（公共API）
 */
export function preloadFonts(fonts) {
    return fontManager.preloadFonts(fonts);
}

/**
 * 获取字体状态（公共API）
 */
export function getFontStatus(fontFamily, fontWeight, fontStyle) {
    return fontManager.getFontStatus(fontFamily, fontWeight, fontStyle);
}

/**
 * 优化的字体加载策略 - 仅在字体文件存在时加载
 */
export function loadOptimizedFonts() {
    // 检测系统字体支持情况
    const supportedFonts = detectSystemFontSupport();
    console.log('支持的系统字体:', supportedFonts);

    // 如果系统已有良好的中文字体支持，则不加载外部字体
    if (supportedFonts.length > 0) {
        console.log('使用系统字体，跳过外部字体加载');
        return Promise.resolve();
    }

    // 仅在开发环境或有可用字体服务时加载外部字体
    const isDevelopment = window.location.hostname === 'localhost' ||
                         window.location.hostname === '127.0.0.1';

    if (isDevelopment) {
        console.log('开发环境：跳过外部字体加载，使用系统字体');
        return Promise.resolve();
    }

    // 生产环境可以启用字体加载（需要确保字体文件存在）
    const fonts = [
        // 暂时注释掉，避免404错误
        /*
        {
            family: 'Source Han Sans SC',
            url: 'https://cdnfe-azure.summerfarm.net/fonts/source-han-sans-sc-regular.woff2',
            options: {
                fontWeight: 'normal',
                fontDisplay: 'swap'
            }
        }
        */
    ];

    if (fonts.length === 0) {
        return Promise.resolve();
    }

    // 延迟加载字体，避免阻塞首屏渲染
    return new Promise((resolve) => {
        setTimeout(() => {
            preloadFonts(fonts).then(resolve).catch((error) => {
                console.warn('字体加载失败，使用系统字体:', error);
                resolve();
            });
        }, 1000);
    });
}

/**
 * 检测系统字体支持
 */
export function detectSystemFontSupport() {
    const systemFonts = [
        'PingFang SC',
        'Hiragino Sans GB',
        'Microsoft YaHei',
        'Source Han Sans SC'
    ];

    const supportedFonts = [];

    systemFonts.forEach(font => {
        const testElement = document.createElement('div');
        testElement.style.fontFamily = font;
        testElement.style.position = 'absolute';
        testElement.style.left = '-9999px';
        testElement.textContent = '测试';
        document.body.appendChild(testElement);

        const computedFont = window.getComputedStyle(testElement).fontFamily;
        if (computedFont.includes(font)) {
            supportedFonts.push(font);
        }

        document.body.removeChild(testElement);
    });

    return supportedFonts;
}
