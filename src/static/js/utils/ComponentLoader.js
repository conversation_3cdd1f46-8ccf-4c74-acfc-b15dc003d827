/**
 * 组件懒加载器 - 实现Vue组件的按需加载
 * 
 * 功能：
 * 1. 支持组件懒加载，减少初始包大小
 * 2. 提供加载状态管理
 * 3. 支持预加载和条件加载
 * 4. 错误处理和重试机制
 * 
 * 使用方式：
 * import { lazyComponent, preloadComponent } from './ComponentLoader.js';
 * const MyComponent = lazyComponent(() => import('./MyComponent.js'));
 */

import { defineAsyncComponent, h } from 'vue';

/**
 * 创建懒加载组件
 * @param {Function} loader - 组件加载函数
 * @param {Object} options - 配置选项
 * @returns {Object} Vue异步组件
 */
export function lazyComponent(loader, options = {}) {
    const {
        loadingComponent = null,
        errorComponent = null,
        delay = 200,
        timeout = 10000,
        suspensible = false,
        retryCount = 3,
        onError = null
    } = options;

    let retries = 0;

    const asyncLoader = async () => {
        try {
            const module = await loader();
            return module.default || module;
        } catch (error) {
            console.warn(`组件加载失败 (尝试 ${retries + 1}/${retryCount}):`, error);
            
            if (retries < retryCount - 1) {
                retries++;
                // 指数退避重试
                await new Promise(resolve => setTimeout(resolve, Math.pow(2, retries) * 1000));
                return asyncLoader();
            }
            
            if (onError) {
                onError(error);
            }
            
            throw error;
        }
    };

    return defineAsyncComponent({
        loader: asyncLoader,
        loadingComponent: loadingComponent || createLoadingComponent(),
        errorComponent: errorComponent || createErrorComponent(),
        delay,
        timeout,
        suspensible
    });
}

/**
 * 创建默认加载组件
 */
function createLoadingComponent() {
    return {
        template: `
            <div class="component-loading">
                <div class="loading-spinner"></div>
                <div class="loading-text">加载中...</div>
            </div>
        `,
        style: `
            .component-loading {
                display: flex;
                flex-direction: column;
                align-items: center;
                justify-content: center;
                padding: 2rem;
                gap: 1rem;
            }
            .loading-text {
                color: var(--color-text-secondary);
                font-size: 0.875rem;
            }
        `
    };
}

/**
 * 创建默认错误组件
 */
function createErrorComponent() {
    return {
        template: `
            <div class="component-error">
                <div class="error-icon">⚠️</div>
                <div class="error-text">组件加载失败</div>
                <button class="retry-btn" @click="$emit('retry')">重试</button>
            </div>
        `,
        emits: ['retry'],
        style: `
            .component-error {
                display: flex;
                flex-direction: column;
                align-items: center;
                justify-content: center;
                padding: 2rem;
                gap: 1rem;
                color: var(--color-error);
            }
            .retry-btn {
                padding: 0.5rem 1rem;
                background: var(--color-primary);
                color: white;
                border: none;
                border-radius: 0.375rem;
                cursor: pointer;
            }
        `
    };
}

/**
 * 预加载组件
 * @param {Function} loader - 组件加载函数
 * @returns {Promise}
 */
export function preloadComponent(loader) {
    return loader().catch(error => {
        console.warn('组件预加载失败:', error);
    });
}

/**
 * 批量预加载组件
 * @param {Array<Function>} loaders - 组件加载函数数组
 * @returns {Promise<Array>}
 */
export function preloadComponents(loaders) {
    return Promise.allSettled(loaders.map(loader => preloadComponent(loader)));
}

/**
 * 条件懒加载组件
 * @param {Function} condition - 加载条件
 * @param {Function} loader - 组件加载函数
 * @param {Function} fallbackLoader - 备用组件加载函数
 * @returns {Object} Vue异步组件
 */
export function conditionalLazyComponent(condition, loader, fallbackLoader = null) {
    return lazyComponent(() => {
        if (condition()) {
            return loader();
        } else if (fallbackLoader) {
            return fallbackLoader();
        } else {
            return Promise.resolve({ template: '<div></div>' });
        }
    });
}

/**
 * 路由级别的懒加载组件
 * @param {Function} loader - 组件加载函数
 * @returns {Function} 路由组件函数
 */
export function lazyRouteComponent(loader) {
    return () => lazyComponent(loader, {
        delay: 100,
        timeout: 15000,
        loadingComponent: {
            template: `
                <div class="route-loading">
                    <div class="loading-spinner"></div>
                    <div>页面加载中...</div>
                </div>
            `
        }
    });
}

/**
 * 智能预加载 - 基于用户行为预测
 */
export class SmartPreloader {
    constructor() {
        this.preloadQueue = new Set();
        this.preloadedComponents = new Map();
        this.isPreloading = false;
    }

    /**
     * 添加到预加载队列
     * @param {string} componentName - 组件名称
     * @param {Function} loader - 加载函数
     * @param {number} priority - 优先级 (1-10)
     */
    addToQueue(componentName, loader, priority = 5) {
        if (this.preloadedComponents.has(componentName)) {
            return;
        }

        this.preloadQueue.add({
            name: componentName,
            loader,
            priority,
            timestamp: Date.now()
        });

        this.schedulePreload();
    }

    /**
     * 调度预加载
     */
    schedulePreload() {
        if (this.isPreloading) return;

        // 在空闲时间执行预加载
        if (window.requestIdleCallback) {
            window.requestIdleCallback(() => this.executePreload());
        } else {
            setTimeout(() => this.executePreload(), 100);
        }
    }

    /**
     * 执行预加载
     */
    async executePreload() {
        if (this.preloadQueue.size === 0) return;

        this.isPreloading = true;

        // 按优先级排序
        const sortedQueue = Array.from(this.preloadQueue).sort((a, b) => b.priority - a.priority);

        for (const item of sortedQueue.slice(0, 3)) { // 每次最多预加载3个组件
            try {
                const component = await item.loader();
                this.preloadedComponents.set(item.name, component);
                this.preloadQueue.delete(item);
                
                console.log(`预加载组件成功: ${item.name}`);
            } catch (error) {
                console.warn(`预加载组件失败: ${item.name}`, error);
                this.preloadQueue.delete(item);
            }

            // 避免阻塞主线程
            await new Promise(resolve => setTimeout(resolve, 50));
        }

        this.isPreloading = false;

        // 如果还有待预加载的组件，继续调度
        if (this.preloadQueue.size > 0) {
            this.schedulePreload();
        }
    }

    /**
     * 获取预加载的组件
     * @param {string} componentName - 组件名称
     * @returns {Object|null} 预加载的组件
     */
    getPreloadedComponent(componentName) {
        return this.preloadedComponents.get(componentName) || null;
    }
}

// 全局智能预加载器实例
export const smartPreloader = new SmartPreloader();

/**
 * 基于交互的预加载
 * @param {string} componentName - 组件名称
 * @param {Function} loader - 加载函数
 * @param {string} trigger - 触发事件 ('hover', 'focus', 'visible')
 * @param {Element} element - 触发元素
 */
export function preloadOnInteraction(componentName, loader, trigger = 'hover', element = null) {
    if (!element) return;

    let hasTriggered = false;

    const preload = () => {
        if (hasTriggered) return;
        hasTriggered = true;
        smartPreloader.addToQueue(componentName, loader, 8);
    };

    switch (trigger) {
        case 'hover':
            element.addEventListener('mouseenter', preload, { once: true });
            break;
        case 'focus':
            element.addEventListener('focus', preload, { once: true });
            break;
        case 'visible':
            if (window.IntersectionObserver) {
                const observer = new IntersectionObserver((entries) => {
                    if (entries[0].isIntersecting) {
                        preload();
                        observer.disconnect();
                    }
                });
                observer.observe(element);
            }
            break;
    }
}
