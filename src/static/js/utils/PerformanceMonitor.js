/**
 * 性能监控工具 - 监测和分析前端性能指标
 * 
 * 功能：
 * 1. 监测Core Web Vitals (FCP, LCP, CLS, FID)
 * 2. 资源加载性能分析
 * 3. 用户体验指标收集
 * 4. 性能数据上报
 * 5. 实时性能建议
 * 
 * 使用方式：
 * import { PerformanceMonitor } from './PerformanceMonitor.js';
 * const monitor = new PerformanceMonitor();
 * monitor.start();
 */

/**
 * 性能监控器
 */
export class PerformanceMonitor {
    constructor(options = {}) {
        this.options = {
            enableReporting: options.enableReporting || false,
            reportEndpoint: options.reportEndpoint || '/api/performance',
            sampleRate: options.sampleRate || 0.1, // 10%采样率
            debug: options.debug || false,
            ...options
        };

        this.metrics = new Map();
        this.observers = [];
        this.startTime = performance.now();
        
        this.init();
    }

    /**
     * 初始化监控器
     */
    init() {
        if (typeof window === 'undefined') return;

        // 监听页面加载完成
        if (document.readyState === 'complete') {
            this.onPageLoad();
        } else {
            window.addEventListener('load', () => this.onPageLoad());
        }

        // 监听页面可见性变化
        document.addEventListener('visibilitychange', () => this.onVisibilityChange());
        
        // 监听页面卸载
        window.addEventListener('beforeunload', () => this.onPageUnload());
    }

    /**
     * 开始监控
     */
    start() {
        this.measureNavigationTiming();
        this.measureCoreWebVitals();
        this.measureResourceTiming();
        this.measureCustomMetrics();
        
        if (this.options.debug) {
            console.log('性能监控已启动');
        }
    }

    /**
     * 测量导航时间
     */
    measureNavigationTiming() {
        if (!performance.getEntriesByType) return;

        const navigation = performance.getEntriesByType('navigation')[0];
        if (!navigation) return;

        const metrics = {
            // DNS查询时间
            dnsLookup: navigation.domainLookupEnd - navigation.domainLookupStart,
            // TCP连接时间
            tcpConnect: navigation.connectEnd - navigation.connectStart,
            // SSL握手时间
            sslHandshake: navigation.secureConnectionStart > 0 ? 
                navigation.connectEnd - navigation.secureConnectionStart : 0,
            // 请求响应时间
            requestResponse: navigation.responseEnd - navigation.requestStart,
            // DOM解析时间
            domParsing: navigation.domContentLoadedEventEnd - navigation.responseEnd,
            // 资源加载时间
            resourceLoading: navigation.loadEventEnd - navigation.domContentLoadedEventEnd,
            // 总页面加载时间
            totalPageLoad: navigation.loadEventEnd - navigation.navigationStart
        };

        this.setMetrics('navigation', metrics);
    }

    /**
     * 测量Core Web Vitals
     */
    measureCoreWebVitals() {
        // First Contentful Paint (FCP)
        this.measureFCP();
        
        // Largest Contentful Paint (LCP)
        this.measureLCP();
        
        // Cumulative Layout Shift (CLS)
        this.measureCLS();
        
        // First Input Delay (FID)
        this.measureFID();
        
        // Time to Interactive (TTI)
        this.measureTTI();
    }

    /**
     * 测量FCP
     */
    measureFCP() {
        if (!window.PerformanceObserver) return;

        try {
            const observer = new PerformanceObserver((list) => {
                const entries = list.getEntries();
                const fcpEntry = entries.find(entry => entry.name === 'first-contentful-paint');
                
                if (fcpEntry) {
                    this.setMetric('fcp', fcpEntry.startTime);
                    observer.disconnect();
                    
                    if (this.options.debug) {
                        console.log(`FCP: ${fcpEntry.startTime.toFixed(2)}ms`);
                    }
                }
            });

            observer.observe({ entryTypes: ['paint'] });
            this.observers.push(observer);
        } catch (error) {
            console.warn('FCP测量失败:', error);
        }
    }

    /**
     * 测量LCP
     */
    measureLCP() {
        if (!window.PerformanceObserver) return;

        try {
            const observer = new PerformanceObserver((list) => {
                const entries = list.getEntries();
                const lastEntry = entries[entries.length - 1];
                
                if (lastEntry) {
                    this.setMetric('lcp', lastEntry.startTime);
                    
                    if (this.options.debug) {
                        console.log(`LCP: ${lastEntry.startTime.toFixed(2)}ms`);
                    }
                }
            });

            observer.observe({ entryTypes: ['largest-contentful-paint'] });
            this.observers.push(observer);
        } catch (error) {
            console.warn('LCP测量失败:', error);
        }
    }

    /**
     * 测量CLS
     */
    measureCLS() {
        if (!window.PerformanceObserver) return;

        try {
            let clsValue = 0;
            let sessionValue = 0;
            let sessionEntries = [];

            const observer = new PerformanceObserver((list) => {
                for (const entry of list.getEntries()) {
                    if (!entry.hadRecentInput) {
                        const firstSessionEntry = sessionEntries[0];
                        const lastSessionEntry = sessionEntries[sessionEntries.length - 1];

                        if (sessionValue && 
                            entry.startTime - lastSessionEntry.startTime < 1000 &&
                            entry.startTime - firstSessionEntry.startTime < 5000) {
                            sessionValue += entry.value;
                            sessionEntries.push(entry);
                        } else {
                            sessionValue = entry.value;
                            sessionEntries = [entry];
                        }

                        if (sessionValue > clsValue) {
                            clsValue = sessionValue;
                            this.setMetric('cls', clsValue);
                            
                            if (this.options.debug) {
                                console.log(`CLS: ${clsValue.toFixed(4)}`);
                            }
                        }
                    }
                }
            });

            observer.observe({ entryTypes: ['layout-shift'] });
            this.observers.push(observer);
        } catch (error) {
            console.warn('CLS测量失败:', error);
        }
    }

    /**
     * 测量FID
     */
    measureFID() {
        if (!window.PerformanceObserver) return;

        try {
            const observer = new PerformanceObserver((list) => {
                const firstInput = list.getEntries()[0];
                if (firstInput) {
                    const fid = firstInput.processingStart - firstInput.startTime;
                    this.setMetric('fid', fid);
                    
                    if (this.options.debug) {
                        console.log(`FID: ${fid.toFixed(2)}ms`);
                    }
                    
                    observer.disconnect();
                }
            });

            observer.observe({ entryTypes: ['first-input'] });
            this.observers.push(observer);
        } catch (error) {
            console.warn('FID测量失败:', error);
        }
    }

    /**
     * 测量TTI（简化版本）
     */
    measureTTI() {
        // 简化的TTI计算：DOM加载完成 + 主线程空闲
        const domContentLoaded = performance.getEntriesByType('navigation')[0]?.domContentLoadedEventEnd;
        
        if (domContentLoaded) {
            // 等待主线程空闲
            setTimeout(() => {
                const tti = performance.now();
                this.setMetric('tti', tti);
                
                if (this.options.debug) {
                    console.log(`TTI: ${tti.toFixed(2)}ms`);
                }
            }, 100);
        }
    }

    /**
     * 测量资源加载性能
     */
    measureResourceTiming() {
        if (!performance.getEntriesByType) return;

        const resources = performance.getEntriesByType('resource');
        const resourceMetrics = {
            totalResources: resources.length,
            totalSize: 0,
            totalDuration: 0,
            byType: {}
        };

        resources.forEach(resource => {
            const type = this.getResourceType(resource.name);
            const duration = resource.responseEnd - resource.startTime;
            const size = resource.transferSize || 0;

            resourceMetrics.totalSize += size;
            resourceMetrics.totalDuration += duration;

            if (!resourceMetrics.byType[type]) {
                resourceMetrics.byType[type] = {
                    count: 0,
                    totalSize: 0,
                    totalDuration: 0,
                    avgDuration: 0
                };
            }

            resourceMetrics.byType[type].count++;
            resourceMetrics.byType[type].totalSize += size;
            resourceMetrics.byType[type].totalDuration += duration;
            resourceMetrics.byType[type].avgDuration = 
                resourceMetrics.byType[type].totalDuration / resourceMetrics.byType[type].count;
        });

        this.setMetrics('resources', resourceMetrics);
    }

    /**
     * 获取资源类型
     */
    getResourceType(url) {
        if (url.includes('.css')) return 'css';
        if (url.includes('.js')) return 'javascript';
        if (url.match(/\.(jpg|jpeg|png|gif|webp|svg)$/i)) return 'image';
        if (url.match(/\.(woff|woff2|ttf|otf)$/i)) return 'font';
        return 'other';
    }

    /**
     * 测量自定义指标
     */
    measureCustomMetrics() {
        // Vue应用挂载时间
        const vueAppMountTime = performance.getEntriesByName('vue-app-mount')[0];
        if (vueAppMountTime) {
            this.setMetric('vueAppMount', vueAppMountTime.duration);
        }

        // CSS加载完成时间
        const cssLoadTime = performance.getEntriesByName('css-loaded')[0];
        if (cssLoadTime) {
            this.setMetric('cssLoad', cssLoadTime.duration);
        }
    }

    /**
     * 设置单个指标
     */
    setMetric(name, value) {
        this.metrics.set(name, {
            value,
            timestamp: Date.now()
        });
    }

    /**
     * 设置多个指标
     */
    setMetrics(category, metrics) {
        this.metrics.set(category, {
            ...metrics,
            timestamp: Date.now()
        });
    }

    /**
     * 获取指标
     */
    getMetric(name) {
        return this.metrics.get(name);
    }

    /**
     * 获取所有指标
     */
    getAllMetrics() {
        const result = {};
        this.metrics.forEach((value, key) => {
            result[key] = value;
        });
        return result;
    }

    /**
     * 生成性能报告
     */
    generateReport() {
        const metrics = this.getAllMetrics();
        const report = {
            timestamp: Date.now(),
            url: window.location.href,
            userAgent: navigator.userAgent,
            viewport: {
                width: window.innerWidth,
                height: window.innerHeight
            },
            connection: this.getConnectionInfo(),
            metrics: metrics,
            recommendations: this.generateRecommendations(metrics)
        };

        return report;
    }

    /**
     * 获取连接信息
     */
    getConnectionInfo() {
        if ('connection' in navigator) {
            const conn = navigator.connection;
            return {
                effectiveType: conn.effectiveType,
                downlink: conn.downlink,
                rtt: conn.rtt,
                saveData: conn.saveData
            };
        }
        return null;
    }

    /**
     * 生成性能建议
     */
    generateRecommendations(metrics) {
        const recommendations = [];

        // FCP建议
        const fcp = metrics.fcp?.value;
        if (fcp > 2000) {
            recommendations.push({
                type: 'fcp',
                severity: 'high',
                message: `FCP过慢 (${fcp.toFixed(0)}ms)，建议优化关键资源加载`
            });
        }

        // LCP建议
        const lcp = metrics.lcp?.value;
        if (lcp > 2500) {
            recommendations.push({
                type: 'lcp',
                severity: 'high',
                message: `LCP过慢 (${lcp.toFixed(0)}ms)，建议优化最大内容元素`
            });
        }

        // CLS建议
        const cls = metrics.cls?.value;
        if (cls > 0.1) {
            recommendations.push({
                type: 'cls',
                severity: 'medium',
                message: `布局偏移过大 (${cls.toFixed(3)})，建议为元素设置固定尺寸`
            });
        }

        return recommendations;
    }

    /**
     * 页面加载完成回调
     */
    onPageLoad() {
        setTimeout(() => {
            const report = this.generateReport();
            
            if (this.options.debug) {
                console.log('性能报告:', report);
            }

            if (this.options.enableReporting && Math.random() < this.options.sampleRate) {
                this.sendReport(report);
            }
        }, 1000);
    }

    /**
     * 页面可见性变化回调
     */
    onVisibilityChange() {
        if (document.hidden) {
            // 页面隐藏时记录时间
            this.setMetric('pageHidden', performance.now());
        } else {
            // 页面显示时记录时间
            this.setMetric('pageVisible', performance.now());
        }
    }

    /**
     * 页面卸载回调
     */
    onPageUnload() {
        // 清理观察器
        this.observers.forEach(observer => observer.disconnect());
    }

    /**
     * 发送性能报告
     */
    async sendReport(report) {
        try {
            await fetch(this.options.reportEndpoint, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(report)
            });
        } catch (error) {
            console.warn('性能报告发送失败:', error);
        }
    }
}

// 全局性能监控器实例
export const performanceMonitor = new PerformanceMonitor({
    debug: process.env.NODE_ENV === 'development',
    enableReporting: process.env.NODE_ENV === 'production'
});

/**
 * 便捷函数：开始性能监控
 */
export function startPerformanceMonitoring(options = {}) {
    const monitor = new PerformanceMonitor(options);
    monitor.start();
    return monitor;
}

/**
 * 便捷函数：标记性能时间点
 */
export function markPerformance(name) {
    if (performance.mark) {
        performance.mark(name);
    }
}

/**
 * 便捷函数：测量性能区间
 */
export function measurePerformance(name, startMark, endMark) {
    if (performance.measure) {
        performance.measure(name, startMark, endMark);
    }
}
