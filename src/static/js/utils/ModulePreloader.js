/**
 * 模块预加载器 - 确保预加载的资源被正确使用
 * 
 * 功能：
 * 1. 管理模块预加载状态
 * 2. 避免预加载资源未使用的警告
 * 3. 优化模块加载时序
 * 4. 提供加载状态反馈
 */

/**
 * 模块预加载管理器
 */
export class ModulePreloader {
    constructor() {
        this.preloadedModules = new Map();
        this.loadingPromises = new Map();
        this.preloadLinks = new Map();
        
        this.init();
    }

    /**
     * 初始化预加载器
     */
    init() {
        // 收集页面中的预加载链接
        this.collectPreloadLinks();
        
        // 监听模块加载事件
        this.setupModuleLoadListener();
    }

    /**
     * 收集预加载链接
     */
    collectPreloadLinks() {
        const preloadLinks = document.querySelectorAll('link[rel="preload"][as="script"]');
        
        preloadLinks.forEach(link => {
            const href = link.href;
            this.preloadLinks.set(href, {
                element: link,
                used: false,
                timestamp: Date.now()
            });
        });
    }

    /**
     * 设置模块加载监听器
     */
    setupModuleLoadListener() {
        // 监听动态导入
        const originalImport = window.import || (async (specifier) => {
            throw new Error('Dynamic import not supported');
        });

        // 包装动态导入以跟踪使用情况
        if (typeof window !== 'undefined') {
            window.addEventListener('beforeunload', () => {
                this.reportUnusedPreloads();
            });
        }
    }

    /**
     * 标记预加载资源为已使用
     */
    markAsUsed(url) {
        // 尝试匹配URL（处理查询参数等）
        for (const [preloadUrl, info] of this.preloadLinks.entries()) {
            if (this.urlsMatch(url, preloadUrl)) {
                info.used = true;
                info.usedAt = Date.now();
                break;
            }
        }
    }

    /**
     * URL匹配检查
     */
    urlsMatch(url1, url2) {
        try {
            const u1 = new URL(url1, window.location.origin);
            const u2 = new URL(url2, window.location.origin);
            
            // 比较路径部分，忽略查询参数
            return u1.pathname === u2.pathname;
        } catch (error) {
            // 如果URL解析失败，进行简单字符串匹配
            return url1.includes(url2) || url2.includes(url1);
        }
    }

    /**
     * 报告未使用的预加载资源
     */
    reportUnusedPreloads() {
        const unused = [];
        const now = Date.now();
        
        this.preloadLinks.forEach((info, url) => {
            if (!info.used && (now - info.timestamp) > 5000) { // 5秒后仍未使用
                unused.push(url);
            }
        });

        if (unused.length > 0 && console.warn) {
            console.warn('未使用的预加载资源:', unused);
        }
    }

    /**
     * 预加载模块
     */
    async preloadModule(specifier, options = {}) {
        if (this.preloadedModules.has(specifier)) {
            return this.preloadedModules.get(specifier);
        }

        if (this.loadingPromises.has(specifier)) {
            return this.loadingPromises.get(specifier);
        }

        const loadPromise = this.loadModule(specifier, options);
        this.loadingPromises.set(specifier, loadPromise);

        try {
            const module = await loadPromise;
            this.preloadedModules.set(specifier, module);
            this.markAsUsed(specifier);
            return module;
        } catch (error) {
            this.loadingPromises.delete(specifier);
            throw error;
        }
    }

    /**
     * 加载模块
     */
    async loadModule(specifier, options = {}) {
        const { timeout = 10000, retries = 3 } = options;
        
        let lastError;
        
        for (let i = 0; i < retries; i++) {
            try {
                const module = await Promise.race([
                    import(specifier),
                    new Promise((_, reject) => {
                        setTimeout(() => reject(new Error('Module load timeout')), timeout);
                    })
                ]);
                
                return module;
            } catch (error) {
                lastError = error;
                
                if (i < retries - 1) {
                    // 指数退避重试
                    await new Promise(resolve => setTimeout(resolve, Math.pow(2, i) * 1000));
                }
            }
        }
        
        throw lastError;
    }

    /**
     * 获取预加载的模块
     */
    getPreloadedModule(specifier) {
        return this.preloadedModules.get(specifier);
    }

    /**
     * 检查模块是否已预加载
     */
    isModulePreloaded(specifier) {
        return this.preloadedModules.has(specifier);
    }

    /**
     * 清理预加载缓存
     */
    clearCache() {
        this.preloadedModules.clear();
        this.loadingPromises.clear();
    }
}

// 全局模块预加载器实例
export const modulePreloader = new ModulePreloader();

/**
 * 优化的动态导入函数
 */
export async function optimizedImport(specifier, options = {}) {
    // 首先检查是否已经预加载
    if (modulePreloader.isModulePreloaded(specifier)) {
        return modulePreloader.getPreloadedModule(specifier);
    }

    // 标记资源为已使用
    modulePreloader.markAsUsed(specifier);

    // 执行导入
    return modulePreloader.preloadModule(specifier, options);
}

/**
 * 批量预加载模块
 */
export async function preloadModules(specifiers, options = {}) {
    const loadPromises = specifiers.map(specifier => {
        if (typeof specifier === 'string') {
            return modulePreloader.preloadModule(specifier, options);
        } else {
            return modulePreloader.preloadModule(specifier.path, { ...options, ...specifier.options });
        }
    });

    return Promise.allSettled(loadPromises);
}

/**
 * 智能模块加载 - 根据网络条件调整策略
 */
export async function smartImport(specifier, options = {}) {
    const connection = navigator.connection || navigator.mozConnection || navigator.webkitConnection;
    
    // 根据网络条件调整超时和重试策略
    const networkOptions = { ...options };
    
    if (connection) {
        if (connection.effectiveType === 'slow-2g' || connection.effectiveType === '2g') {
            networkOptions.timeout = 20000; // 慢网络增加超时时间
            networkOptions.retries = 1; // 减少重试次数
        } else if (connection.effectiveType === '4g') {
            networkOptions.timeout = 5000; // 快网络减少超时时间
            networkOptions.retries = 3;
        }
    }

    return optimizedImport(specifier, networkOptions);
}

/**
 * 条件模块加载
 */
export async function conditionalImport(condition, specifier, fallbackSpecifier = null) {
    const shouldLoad = typeof condition === 'function' ? condition() : condition;
    
    if (shouldLoad) {
        return optimizedImport(specifier);
    } else if (fallbackSpecifier) {
        return optimizedImport(fallbackSpecifier);
    } else {
        return null;
    }
}

/**
 * 延迟模块加载
 */
export function deferredImport(specifier, delay = 0, options = {}) {
    return new Promise((resolve, reject) => {
        setTimeout(() => {
            optimizedImport(specifier, options).then(resolve).catch(reject);
        }, delay);
    });
}

/**
 * 模块加载状态监控
 */
export function getModuleLoadStats() {
    const stats = {
        preloaded: modulePreloader.preloadedModules.size,
        loading: modulePreloader.loadingPromises.size,
        preloadLinks: modulePreloader.preloadLinks.size,
        unusedPreloads: 0
    };

    // 计算未使用的预加载资源
    modulePreloader.preloadLinks.forEach(info => {
        if (!info.used) {
            stats.unusedPreloads++;
        }
    });

    return stats;
}
