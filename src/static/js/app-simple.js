/**
 * 简化版应用启动器 - 用于调试白屏问题
 */

import { createApp } from 'vue';

// 检查是否是共享会话
const isSharedConversation = window.sharedConversation && window.sharedConversation.isShared;

// 创建简单的测试组件
const TestLayout = {
    name: 'TestLayout',
    template: `
        <div style="
            padding: 2rem;
            max-width: 800px;
            margin: 0 auto;
            font-family: -apple-system, BlinkMacSystemFont, 'PingFang SC', sans-serif;
        ">
            <h1 style="color: #1f2937; margin-bottom: 1rem;">
                🎉 ChatBI 应用正常运行！
            </h1>
            <div style="
                background: #f0f9ff;
                border: 1px solid #0ea5e9;
                border-radius: 0.5rem;
                padding: 1rem;
                margin-bottom: 1rem;
            ">
                <h2 style="color: #0369a1; margin: 0 0 0.5rem 0;">性能优化成功</h2>
                <p style="margin: 0; color: #0c4a6e;">
                    应用已成功加载，性能优化措施正在生效。
                </p>
            </div>
            <div style="
                background: #f0fdf4;
                border: 1px solid #22c55e;
                border-radius: 0.5rem;
                padding: 1rem;
                margin-bottom: 1rem;
            ">
                <h3 style="color: #15803d; margin: 0 0 0.5rem 0;">当前状态</h3>
                <ul style="margin: 0; padding-left: 1.5rem; color: #166534;">
                    <li>Vue.js: 正常加载</li>
                    <li>组件系统: 正常工作</li>
                    <li>样式系统: 正常应用</li>
                    <li>性能监控: {{ performanceStatus }}</li>
                </ul>
            </div>
            <div style="
                background: #fefce8;
                border: 1px solid #eab308;
                border-radius: 0.5rem;
                padding: 1rem;
            ">
                <h3 style="color: #a16207; margin: 0 0 0.5rem 0;">下一步</h3>
                <p style="margin: 0; color: #92400e;">
                    现在可以安全地切换回完整版本的应用。
                </p>
            </div>
        </div>
    `,
    setup() {
        const performanceStatus = window.performanceMonitor ? '已启用' : '未启用';
        
        return {
            performanceStatus
        };
    }
};

const ShareTestLayout = {
    name: 'ShareTestLayout',
    template: `
        <div style="
            padding: 2rem;
            max-width: 800px;
            margin: 0 auto;
            font-family: -apple-system, BlinkMacSystemFont, 'PingFang SC', sans-serif;
        ">
            <h1 style="color: #1f2937; margin-bottom: 1rem;">
                🔗 共享会话模式
            </h1>
            <div style="
                background: #fef3c7;
                border: 1px solid #f59e0b;
                border-radius: 0.5rem;
                padding: 1rem;
            ">
                <p style="margin: 0; color: #92400e;">
                    当前处于共享会话模式，应用正常运行。
                </p>
            </div>
        </div>
    `
};

// 显示加载状态
function showLoadingState() {
    const app = document.getElementById('app');
    app.innerHTML = `
        <div style="
            display: flex;
            justify-content: center;
            align-items: center;
            height: 100vh;
            flex-direction: column;
            gap: 1rem;
            font-family: -apple-system, BlinkMacSystemFont, 'PingFang SC', sans-serif;
        ">
            <div style="
                width: 40px;
                height: 40px;
                border: 4px solid #f3f4f6;
                border-top: 4px solid #3b82f6;
                border-radius: 50%;
                animation: spin 1s linear infinite;
            "></div>
            <div style="color: #6b7280; font-size: 0.875rem;">
                正在加载应用...
            </div>
        </div>
    `;
}

// 启动简化版应用
async function startSimpleApp() {
    try {
        console.log('🚀 启动简化版应用进行测试');
        
        // 显示加载状态
        showLoadingState();
        
        // 短暂延迟以显示加载状态
        await new Promise(resolve => setTimeout(resolve, 500));
        
        // 创建Vue应用实例
        const app = createApp({
            components: {
                TestLayout,
                ShareTestLayout
            },
            setup() {
                return {
                    isSharedConversation
                };
            },
            template: `
                <ShareTestLayout v-if="isSharedConversation" />
                <TestLayout v-else />
            `
        });

        // 挂载应用到DOM
        app.mount('#app');
        
        console.log('✅ 简化版应用启动成功');
        
        // 启动性能监控（如果可用）
        if (window.performanceMonitor) {
            window.performanceMonitor.start();
        }
        
    } catch (error) {
        console.error('❌ 简化版应用启动失败:', error);
        
        // 显示错误状态
        const app = document.getElementById('app');
        app.innerHTML = `
            <div style="
                display: flex;
                justify-content: center;
                align-items: center;
                height: 100vh;
                flex-direction: column;
                gap: 1rem;
                color: #ef4444;
                font-family: -apple-system, BlinkMacSystemFont, 'PingFang SC', sans-serif;
            ">
                <div style="font-size: 1.5rem;">⚠️ 应用启动失败</div>
                <div style="font-size: 0.875rem; color: #6b7280; text-align: center; max-width: 400px;">
                    错误信息: ${error.message}
                </div>
                <button 
                    onclick="window.location.reload()" 
                    style="
                        padding: 0.5rem 1rem;
                        background: #3b82f6;
                        color: white;
                        border: none;
                        border-radius: 0.375rem;
                        cursor: pointer;
                        font-size: 0.875rem;
                    "
                >
                    刷新页面
                </button>
            </div>
        `;
    }
}

// 等待DOM加载完成后启动应用
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', startSimpleApp);
} else {
    startSimpleApp();
}

// 添加旋转动画的CSS
const style = document.createElement('style');
style.textContent = `
    @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }
`;
document.head.appendChild(style);
