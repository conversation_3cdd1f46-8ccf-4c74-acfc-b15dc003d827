/**
 * 性能验证脚本 - 在浏览器控制台中运行
 * 
 * 使用方法：
 * 1. 打开浏览器开发者工具
 * 2. 在控制台中粘贴并运行此脚本
 * 3. 查看性能报告和建议
 */

(function() {
    'use strict';

    console.log('🚀 ChatBI 性能检查工具');
    console.log('='.repeat(40));

    /**
     * 检查Core Web Vitals
     */
    function checkCoreWebVitals() {
        console.log('\n📊 Core Web Vitals 检查:');
        
        // 检查FCP
        const fcpEntries = performance.getEntriesByName('first-contentful-paint');
        if (fcpEntries.length > 0) {
            const fcp = fcpEntries[0].startTime;
            const fcpStatus = fcp < 2000 ? '✅ 优秀' : fcp < 4000 ? '⚠️ 需要改进' : '❌ 较差';
            console.log(`   FCP: ${fcp.toFixed(0)}ms ${fcpStatus}`);
        }

        // 检查LCP
        if (window.PerformanceObserver) {
            try {
                new PerformanceObserver((list) => {
                    const entries = list.getEntries();
                    const lastEntry = entries[entries.length - 1];
                    if (lastEntry) {
                        const lcp = lastEntry.startTime;
                        const lcpStatus = lcp < 2500 ? '✅ 优秀' : lcp < 4000 ? '⚠️ 需要改进' : '❌ 较差';
                        console.log(`   LCP: ${lcp.toFixed(0)}ms ${lcpStatus}`);
                    }
                }).observe({ entryTypes: ['largest-contentful-paint'] });
            } catch (error) {
                console.log('   LCP: 无法测量');
            }
        }

        // 检查CLS
        if (window.PerformanceObserver) {
            try {
                let clsValue = 0;
                new PerformanceObserver((list) => {
                    for (const entry of list.getEntries()) {
                        if (!entry.hadRecentInput) {
                            clsValue += entry.value;
                        }
                    }
                    const clsStatus = clsValue < 0.1 ? '✅ 优秀' : clsValue < 0.25 ? '⚠️ 需要改进' : '❌ 较差';
                    console.log(`   CLS: ${clsValue.toFixed(3)} ${clsStatus}`);
                }).observe({ entryTypes: ['layout-shift'] });
            } catch (error) {
                console.log('   CLS: 无法测量');
            }
        }
    }

    /**
     * 检查资源加载性能
     */
    function checkResourcePerformance() {
        console.log('\n📦 资源加载性能:');
        
        const resources = performance.getEntriesByType('resource');
        const resourceStats = {
            css: { count: 0, totalSize: 0, totalTime: 0 },
            js: { count: 0, totalSize: 0, totalTime: 0 },
            image: { count: 0, totalSize: 0, totalTime: 0 },
            font: { count: 0, totalSize: 0, totalTime: 0 },
            other: { count: 0, totalSize: 0, totalTime: 0 }
        };

        resources.forEach(resource => {
            const type = getResourceType(resource.name);
            const duration = resource.responseEnd - resource.startTime;
            const size = resource.transferSize || 0;

            resourceStats[type].count++;
            resourceStats[type].totalSize += size;
            resourceStats[type].totalTime += duration;
        });

        Object.entries(resourceStats).forEach(([type, stats]) => {
            if (stats.count > 0) {
                const avgTime = (stats.totalTime / stats.count).toFixed(0);
                const totalSize = (stats.totalSize / 1024).toFixed(1);
                console.log(`   ${type.toUpperCase()}: ${stats.count}个文件, ${totalSize}KB, 平均${avgTime}ms`);
            }
        });
    }

    /**
     * 获取资源类型
     */
    function getResourceType(url) {
        if (url.includes('.css')) return 'css';
        if (url.includes('.js')) return 'js';
        if (url.match(/\.(jpg|jpeg|png|gif|webp|svg)$/i)) return 'image';
        if (url.match(/\.(woff|woff2|ttf|otf)$/i)) return 'font';
        return 'other';
    }

    /**
     * 检查预加载资源使用情况
     */
    function checkPreloadUsage() {
        console.log('\n🔗 预加载资源检查:');
        
        const preloadLinks = document.querySelectorAll('link[rel="preload"]');
        console.log(`   发现 ${preloadLinks.length} 个预加载资源`);

        preloadLinks.forEach((link, index) => {
            const href = link.href;
            const as = link.getAttribute('as');
            console.log(`   ${index + 1}. ${as}: ${href.split('/').pop()}`);
        });
    }

    /**
     * 检查Vue应用状态
     */
    function checkVueAppStatus() {
        console.log('\n⚡ Vue应用状态:');
        
        const app = document.getElementById('app');
        if (app && app.__vue_app__) {
            console.log('   ✅ Vue应用已挂载');
        } else {
            console.log('   ❌ Vue应用未挂载或挂载失败');
        }

        // 检查Vue版本
        if (window.Vue) {
            console.log(`   Vue版本: ${window.Vue.version || '未知'}`);
        }
    }

    /**
     * 检查性能监控器状态
     */
    function checkPerformanceMonitor() {
        console.log('\n📈 性能监控器状态:');
        
        if (window.performanceMonitor) {
            console.log('   ✅ 性能监控器已启用');
            
            try {
                const report = window.performanceMonitor.generateReport();
                console.log('   📊 当前性能指标:');
                
                if (report.metrics.fcp) {
                    console.log(`      FCP: ${report.metrics.fcp.value.toFixed(0)}ms`);
                }
                if (report.metrics.lcp) {
                    console.log(`      LCP: ${report.metrics.lcp.value.toFixed(0)}ms`);
                }
                if (report.metrics.cls) {
                    console.log(`      CLS: ${report.metrics.cls.value.toFixed(3)}`);
                }
                
                if (report.recommendations && report.recommendations.length > 0) {
                    console.log('   💡 优化建议:');
                    report.recommendations.forEach(rec => {
                        console.log(`      ${rec.message}`);
                    });
                }
            } catch (error) {
                console.log('   ⚠️ 无法获取性能报告:', error.message);
            }
        } else {
            console.log('   ❌ 性能监控器未启用');
        }
    }

    /**
     * 检查模块加载器状态
     */
    function checkModuleLoader() {
        console.log('\n🔧 模块加载器状态:');
        
        if (window.modulePreloader) {
            const stats = window.modulePreloader.getModuleLoadStats?.();
            if (stats) {
                console.log(`   预加载模块: ${stats.preloaded}个`);
                console.log(`   正在加载: ${stats.loading}个`);
                console.log(`   预加载链接: ${stats.preloadLinks}个`);
                console.log(`   未使用预加载: ${stats.unusedPreloads}个`);
            } else {
                console.log('   ✅ 模块加载器已启用');
            }
        } else {
            console.log('   ❌ 模块加载器未启用');
        }
    }

    /**
     * 生成性能建议
     */
    function generateRecommendations() {
        console.log('\n💡 性能优化建议:');
        
        const recommendations = [];

        // 检查FCP
        const fcpEntries = performance.getEntriesByName('first-contentful-paint');
        if (fcpEntries.length > 0 && fcpEntries[0].startTime > 2000) {
            recommendations.push('FCP过慢，建议优化关键资源加载');
        }

        // 检查资源数量
        const resources = performance.getEntriesByType('resource');
        if (resources.length > 50) {
            recommendations.push('资源文件过多，建议合并和压缩');
        }

        // 检查大文件
        const largeResources = resources.filter(r => (r.transferSize || 0) > 500 * 1024);
        if (largeResources.length > 0) {
            recommendations.push(`发现${largeResources.length}个大文件(>500KB)，建议优化`);
        }

        if (recommendations.length === 0) {
            console.log('   🎉 当前性能表现良好！');
        } else {
            recommendations.forEach((rec, index) => {
                console.log(`   ${index + 1}. ${rec}`);
            });
        }
    }

    /**
     * 运行所有检查
     */
    function runAllChecks() {
        checkCoreWebVitals();
        checkResourcePerformance();
        checkPreloadUsage();
        checkVueAppStatus();
        checkPerformanceMonitor();
        checkModuleLoader();
        generateRecommendations();
        
        console.log('\n✅ 性能检查完成！');
        console.log('\n📋 如需详细的Lighthouse报告，请运行:');
        console.log('   node scripts/performance-test.js ' + window.location.href + ' --mobile');
    }

    // 等待页面加载完成后运行检查
    if (document.readyState === 'complete') {
        runAllChecks();
    } else {
        window.addEventListener('load', () => {
            setTimeout(runAllChecks, 1000); // 等待1秒确保所有资源加载完成
        });
    }

    // 将检查函数暴露到全局，方便手动调用
    window.performanceCheck = {
        runAll: runAllChecks,
        checkCoreWebVitals,
        checkResourcePerformance,
        checkPreloadUsage,
        checkVueAppStatus,
        checkPerformanceMonitor,
        checkModuleLoader,
        generateRecommendations
    };

})();
