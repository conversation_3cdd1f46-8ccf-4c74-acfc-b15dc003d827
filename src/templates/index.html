<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="icon" type="image/x-icon" href="https://azure.summerfarm.net/favicon.ico">
    <title>ChatBI - 智能数据分析助手</title>

    <!-- 关键资源预加载 - 提升FCP性能 -->
    <link rel="preload" href="{{ url_for('static', filename='css/main.css', t=cache_control_timestamp) }}" as="style">
    <link rel="preload" href="https://unpkg.com/vue@3.5.13/dist/vue.esm-browser.prod.js" as="script" crossorigin="anonymous">
    <link rel="preload" href="{{ url_for('static', filename='js/app.js', t=cache_control_timestamp) }}" as="script" crossorigin="anonymous">

    <!-- DNS预解析 - 减少外部资源加载延迟 -->
    <link rel="dns-prefetch" href="//cdnfe-azure.summerfarm.net">
    <link rel="dns-prefetch" href="//unpkg.com">

    <!-- 关键CSS内联 - 避免渲染阻塞 -->
    <style>
        /* 关键路径CSS - 基础布局和字体 */
        html, body {
            margin: 0;
            padding: 0;
            height: 100%;
            font-family: -apple-system, BlinkMacSystemFont, 'Source Han Sans SC', 'Noto Sans SC', 'PingFang SC', sans-serif;
            font-size: 16px;
            line-height: 1.5;
            -webkit-font-smoothing: antialiased;
        }
        #app {
            min-height: 100vh;
            position: relative;
        }
        .bg-gradient-animated {
            background: linear-gradient(-45deg, #ee7752, #e73c7e, #23a6d5, #23d5ab);
            background-size: 400% 400%;
            animation: gradient 15s ease infinite;
        }
        @keyframes gradient {
            0%, 100% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
        }
        /* 加载状态样式 */
        .loading-spinner {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid #f3f3f3;
            border-top: 3px solid #3498db;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>

    <!-- 延迟加载Tailwind CSS - 避免阻塞首屏渲染 -->
    <script>
        // 延迟加载Tailwind CSS，避免预加载警告
        window.addEventListener('DOMContentLoaded', function() {
            const script = document.createElement('script');
            script.src = "{{ url_for('static', filename='js/lib/tailwindcss-3.4.16.js', t=cache_control_timestamp) }}";
            script.async = true;
            document.head.appendChild(script);
        });
    </script>
    <noscript><script src="{{ url_for('static', filename='js/lib/tailwindcss-3.4.16.js', t=cache_control_timestamp) }}"></script></noscript>

    <!-- DaisyUI CSS - 延迟加载 -->
    <link rel="preload" href="https://cdnfe-azure.summerfarm.net/common/jstest/full.css" as="style" onload="this.onload=null;this.rel='stylesheet'">
    <noscript><link href="https://cdnfe-azure.summerfarm.net/common/jstest/full.css" rel="stylesheet"></noscript>
    <!-- Tailwind Config - 延迟执行避免阻塞 -->
    <script>
        // 延迟配置Tailwind，等待库加载完成
        window.addEventListener('DOMContentLoaded', function() {
            if (window.tailwind) {
                tailwind.config = {
                    theme: {
                        extend: {
                            animation: {
                                'gradient': 'gradient 15s ease infinite',
                            },
                            keyframes: {
                                gradient: {
                                    '0%, 100%': { backgroundPosition: '0% 50%' },
                                    '50%': { backgroundPosition: '100% 50%' },
                                },
                            },
                        },
                    },
                    daisyui: {
                        themes: ["light", "dark"],
                        darkTheme: "dark",
                    },
                }
            }
        });
    </script>

    <!-- Import Maps for Vue.js -->
    <script type="importmap">
        {
            "imports": {
                "vue": "https://unpkg.com/vue@3.5.13/dist/vue.esm-browser.prod.js"
            }
        }
    </script>

    <!-- 主要CSS样式 - 已预加载，现在应用 -->
    <link rel="stylesheet" href="{{ url_for('static', filename='css/main.css', t=cache_control_timestamp) }}" />

    <!-- 内联样式已移动到组件CSS文件中 -->
</head>

<body class="min-h-screen bg-base-100">
    <div class="fixed inset-0 bg-gradient-animated -z-10"></div>

    <div id="app" class="relative min-h-screen">
        <!-- 应用将在这里挂载 -->
    </div>


    <!-- 非关键资源延迟加载 - 提升FCP性能 -->
    <!-- Marked.js - Markdown 解析库 - 延迟加载 -->
    <script>
        // 延迟加载Markdown相关资源
        window.addEventListener('load', function() {
            const scripts = [
                'https://cdnfe-azure.summerfarm.net/common/jstest/marked.min.js',
                'https://cdnfe-azure.summerfarm.net/common/jstest/index.umd.min.js',
                'https://cdnfe-azure.summerfarm.net/common/jstest/highlight.min.js',
                'https://cdnfe-azure.summerfarm.net/common/jstest/sql.min.js'
            ];

            scripts.forEach(function(src, index) {
                setTimeout(function() {
                    const script = document.createElement('script');
                    script.src = src;
                    script.async = true;
                    document.head.appendChild(script);
                }, index * 100); // 错开加载时间
            });
        });
    </script>

    <!-- Highlight.js 主题样式 - 使用媒体查询优化 -->
    <link rel="stylesheet" href="https://cdnfe-azure.summerfarm.net/common/jstest/atom-one-light.min.css"
        id="light-hljs-theme" media="(prefers-color-scheme: light)">
    <link rel="stylesheet" href="https://cdnfe-azure.summerfarm.net/common/jstest/atom-one-dark.min.css"
        id="dark-hljs-theme" media="(prefers-color-scheme: dark)">

    <!-- 延迟初始化 Markdown 和代码高亮 - 避免阻塞首屏渲染 -->
    <script type="module">
        // 延迟加载和初始化非关键功能
        window.addEventListener('load', function() {
            // 等待所有资源加载完成后再初始化
            setTimeout(async function() {
                try {
                    // 等待外部依赖加载完成
                    let retries = 0;
                    const maxRetries = 10;

                    while ((!window.marked || !window.hljs) && retries < maxRetries) {
                        await new Promise(resolve => setTimeout(resolve, 500));
                        retries++;
                    }

                    if (!window.marked || !window.hljs) {
                        console.warn('Markdown依赖未加载完成，跳过初始化');
                        return;
                    }

                    const [
                        { initializeMarked },
                        { initializeHighlighter, updateCodeHighlightTheme }
                    ] = await Promise.all([
                        import("{{ url_for('static', filename='js/utils/MarkdownRenderer.js', t=cache_control_timestamp) }}"),
                        import("{{ url_for('static', filename='js/utils/CodeHighlighter.js', t=cache_control_timestamp) }}")
                    ]);

                    // 初始化marked.js
                    if (initializeMarked) {
                        initializeMarked();
                    }

                    // 初始化highlight.js
                    if (initializeHighlighter) {
                        initializeHighlighter();
                    }

                    // 根据当前主题切换代码高亮主题
                    if (updateCodeHighlightTheme) {
                        updateCodeHighlightTheme(document.documentElement.getAttribute('data-theme') || 'light');
                    }

                    // 监听主题变化
                    const themeObserver = new MutationObserver(function (mutations) {
                        mutations.forEach(function (mutation) {
                            if (mutation.attributeName === 'data-theme') {
                                const theme = document.documentElement.getAttribute('data-theme') || 'light';
                                if (updateCodeHighlightTheme) {
                                    updateCodeHighlightTheme(theme);
                                }
                            }
                        });
                    });

                    // 观察html元素的data-theme属性变化
                    themeObserver.observe(document.documentElement, {
                        attributes: true,
                        attributeFilter: ['data-theme']
                    });
                } catch (error) {
                    console.warn('延迟加载Markdown/代码高亮功能失败:', error);
                }
            }, 2000); // 延迟2秒加载，确保依赖已加载
        });
    </script>

    <!-- 主应用脚本 - 临时使用简化版本进行测试 -->
    <script type="module" src="{{ url_for('static', filename='js/app-simple.js', t=cache_control_timestamp) }}"></script>
    <!-- 原版本备份: <script type="module" src="{{ url_for('static', filename='js/app.js', t=cache_control_timestamp) }}"></script> -->

    <!-- 用户信息传递给前端 -->
    <script>
        window.userInfo = {
            name: "{{ user_name }}",
            avatar: "{{ user_avatar_thumb }}",
            email: "{{ user_email }}",
            jobTitle: "{{ job_title }}",
            isAdmin: {{ 'true' if is_admin else 'false' }},
        appRoot: "{{ app_root }}"
    };

        {% if is_shared %}
        window.sharedConversation = {
            isShared: true,
            isOwner: {{ 'true' if is_owner else 'false' }},
        shareId: "{{ share_id }}",
            ownerName: "{{ share_owner_name }}",
                messages: {{ shared_conversation | tojson }}
        };
        {% else %}
        window.sharedConversation = {
            isShared: false
        };
        {% endif %}
    </script>
</body>


</html>