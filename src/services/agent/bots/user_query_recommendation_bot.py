"""
用户查询推荐机器人模块。
"""

from typing import Dict, Any, List, Optional

from agents import Agent, Model, Runner, ModelSettings

from src.models.user_info_class import UserInfo  # 假设 UserInfo 用于上下文类型
from src.services.agent.bots.base_bot import BaseBot
from src.services.agent.utils.model_provider import get_fast_model
from src.utils.logger import logger
import re
import os


class UserQueryRecommendationBot(BaseBot):
    """
    用户查询推荐机器人。
    该机器人调用 LITE_LLM_FAST_MODEL，根据用户聊天历史和他人聊天历史，
    为用户生成针对性的问题推荐。
    """

    def __init__(self, user_info: Dict[str, Any], count: int = 6, word_limit: int = 50):
        """
        初始化用户查询推荐机器人。

        Args:
            user_info (Dict[str, Any]): 用户信息字典。
        """
        super().__init__(user_info)
        self.count = count
        self.word_limit = word_limit
        # 加载指定markdown文件的内容作为系统提示补充
        self.agent_system_contexts = self._load_agent_contexts()
        
    def _load_agent_contexts(self) -> str:
        """
        从指定的markdown文件中加载agent上下文信息，用于增强推荐问题的相关度。
        
        专注于sales_order_analytics.md、warehouse_and_fulfillment.md、sales_kpi_analytics.md
        这三个核心业务agent的内容。
        
        Returns:
            str: 合并后的agent上下文信息文本。
        """
        context_parts = []
        
        # 定义要加载的markdown文件路径
        markdown_files = [
            "resources/prompt/sales_order_analytics.md",
            "resources/prompt/warehouse_and_fulfillment.md", 
            "resources/prompt/sales_kpi_analytics.md"
        ]
        
        # 项目根目录路径
        project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(__file__))))
        
        for file_path in markdown_files:
            full_path = os.path.join(project_root, file_path)
            try:
                with open(full_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                    # 提取关键信息，去除markdown格式标记
                    content = re.sub(r'^#+\s*', '\n', content, flags=re.MULTILINE)
                    content = re.sub(r'\*\*([^*]+)\*\*', r'\1', content)
                    content = re.sub(r'`([^`]+)`', r'\1', content)
                    context_parts.append(content.strip())
            except FileNotFoundError:
                logger.warning(f"未找到agent上下文文件: {full_path}")
                continue
            except Exception as e:
                logger.warning(f"加载agent上下文文件失败 {full_path}: {e}")
                continue
        
        # 合并所有上下文内容
        combined_context = "\n\n---\n\n".join(context_parts)
        return combined_context

    def get_description(self) -> str:
        """
        获取机器人的描述信息。

        Returns:
            str: 机器人的描述。
        """
        return "我是一个用户查询推荐机器人，可以根据您的历史提问和大家的常见问题，为您推荐一些您可能感兴趣的新问题方向。"

    def _create_recommendation_prompt_text(self) -> str:
        """
        创建用于问题推荐的LLM提示文本。

        Returns:
            str: LLM提示文本。
        """
        agent_context = self.agent_system_contexts
        
        prompt = f"""
你是用户问题推荐助手，专门基于用户聊天记录和业务上下文生成高相关度的推荐问题。

## 业务上下文信息
以下是核心业务agent的专业知识，请充分利用这些信息来生成更精准、更具业务价值的推荐问题：

{agent_context}

## 生成规则
你的核心任务是根据用户提供的聊天记录和上述业务上下文，生成{self.count}条用户可能感兴趣的、结合业务场景的推荐问题。

请严格遵守以下规则：
1.  深度分析"用户的最新消息"和"其他用户的消息"提供的业务需求线索
2.  结合销售订单分析、仓储物流、销售绩效这三个核心业务领域的知识生成问题
3.  生成正好{self.count}条推荐问题，每条必须控制在{self.word_limit}个汉字以内
4.  问题应该基于真实业务场景，包含明确的时间范围、分析维度和业务指标
5.  推荐问题应该涉及：订单分析、库存优化、销售绩效、仓库运营、客户行为分析等具体场景
6.  问题应该是探索性的，能帮助用户发现运营优化点和商机
7.  直接返回{self.count}条问题，每条问题占一行
8.  避免重复已有问题，提供新的业务视角

## 推荐问题类型示例
- 销售订单维度：分析某个时间段某类商品的销售趋势、客户购买行为
- 仓储物流维度：查询某商品在各个仓库的库存状态和出入库情况
- 销售绩效维度：分析某个BD的销售业绩、客户转化、商品推广效果
- 客户价值维度：识别高价值客户、流失风险客户、新客户增长
- 商品运营维度：分析爆款商品、滞销商品、库存周转率

输入示例格式:
## 用户的最新消息:
1. 我的客户中，过去30天购买了安佳淡奶油的有哪些？列出他们的详细信息。
2. 杭州市过去7天新增了多少新注册的门店？
其他用户的消息也类似

输出要求:
我的华东地区客户过去一个月购买安佳淡奶油的订单中，履约率最低的是哪些门店？请分析影响履约的具体原因
过去两周嘉兴仓热销的C味系列产品各SKU的库存变化趋势如何，是否存在缺货风险
我的销售团队中完成新客户拉新目标达成率前10名的BD在推广哪些商品品类时表现最突出
"""
        return prompt.strip()

    def create_agent(self, model: Model = None) -> Agent:
        """
        创建用于生成问题推荐的Agent实例。

        Args:
            model (Model, optional): Agent使用的语言模型。默认为快速模型。

        Returns:
            Agent: 配置好的Agent实例。
        """
        if model is None:
            model = get_fast_model()

        instruction = self._create_recommendation_prompt_text()

        # 记录部分指令用于调试
        logger.info(
            f"UserQueryRecommendationBot instruction (first 200 chars): {instruction[:200]}..."
        )

        # 假设 UserInfo 是标准的上下文类型，与 MasterControllerBot 保持一致
        agent = Agent[UserInfo](
            name="问题推荐助手",
            instructions=instruction,
            model=model,  # 使用指定的快速模型
            model_settings=ModelSettings(
                extra_body={
                    "provider": {
                        "sort": "latency",
                        "quantizations": ["fp8", "int8"],
                    }
                }
            ),
        )
        return agent

    async def get_recommendations(
        self,
        current_user_messages: List[str],
        other_users_messages: List[str],
    ) -> List[str]:
        """
        异步获取问题推荐列表。

        此方法会调用LLM Agent来生成推荐。调用者需要负责从数据库或其他来源
        获取用户的聊天记录，并提取消息内容列表传入。

        Args:
            current_user_messages (List[str]): 当前用户最新的消息内容列表。
            other_users_messages (List[str]): 其他用户的一些消息内容列表。

        Returns:
            List[str]: 生成的推荐问题列表 (最多self.count条，每条不超过self.word_limit个字)。若出错则返回空列表。
        """
        agent = self.create_agent()

        # 构建输入给Agent的文本
        input_parts = []
        if current_user_messages:
            input_parts.append(
                "## 用户的最新消息:\n"
                + "\n".join(f"- {msg}" for msg in current_user_messages)
            )
        else:
            input_parts.append("## 用户的最新消息:\n- (当前用户近期无消息)")

        if other_users_messages:
            input_parts.append(
                "\n\n## 其他用户的消息:\n"
                + "\n".join(f"- {msg}" for msg in other_users_messages)
            )
        else:
            input_parts.append("\n\n## 其他用户的消息:\n- (无其他用户消息参考)")

        agent_input_text = "".join(input_parts)

        logger.info(
            f"UserQueryRecommendationBot agent input (first 300 chars): {agent_input_text[:300]}..."
        )

        try:
            # 运行Agent并获取响应
            response_message = await Runner.run(agent, agent_input_text)

            # 解析LLM的响应内容
            # 假设LLM按要求每行返回一个问题
            raw_recommendations = f"{response_message.final_output}".split("\n")
            logger.info(f"Raw recommendations: {raw_recommendations}")

            valid_recommendations = []
            for rec in raw_recommendations:
                stripped_rec = rec.strip()
                # 去掉markdown的list前缀，仅去掉开头的‘-’或数字加点（如'1. '）
                stripped_rec = re.sub(r"^(?:\d+\.\s*|\-\s*)", "", stripped_rec)
                # 过滤空行和过长的问题(允许超过5个字符)
                if stripped_rec and len(stripped_rec) <= self.word_limit + 5:
                    valid_recommendations.append(stripped_rec)
                else:
                    logger.warning(f"Invalid recommendation skipped: {stripped_rec}")

            # 确保返回count条推荐
            final_recommendations = valid_recommendations[: self.count]
            if len(final_recommendations) < self.count:
                logger.warning(
                    f"Not enough recommendations generated, returning {len(final_recommendations)} instead of {self.count}"
                )

            logger.info(
                f"Generated {len(final_recommendations)} recommendations: {final_recommendations} (count={self.count})"
            )
            return final_recommendations
        except Exception as e:
            logger.error(
                f"Error during UserQueryRecommendationBot agent execution: {e}",
                exc_info=True,
            )
            return []
