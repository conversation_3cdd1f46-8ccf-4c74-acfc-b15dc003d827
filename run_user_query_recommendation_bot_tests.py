#!/usr/bin/env python3
"""
运行用户查询推荐机器人测试的脚本。
"""
import subprocess
import sys
import os

def run_tests():
    """运行测试并显示结果。"""
    test_file = "tests/services/agent/bots/test_user_query_recommendation_bot.py"
    
    # 检查测试文件是否存在
    if not os.path.exists(test_file):
        print(f"❌ 测试文件不存在: {test_file}")
        return False
    
    print("🧪 开始运行用户查询推荐机器人测试...")
    print("=" * 60)
    
    try:
        # 运行pytest
        result = subprocess.run([
            sys.executable, "-m", "pytest", 
            test_file,
            "-v",  # 详细输出
            "--tb=short",  # 简短的错误追踪
            "--color=yes"  # 彩色输出
        ], capture_output=True, text=True)
        
        print("📋 测试输出:")
        print(result.stdout)
        
        if result.stderr:
            print("⚠️  错误信息:")
            print(result.stderr)
        
        if result.returncode == 0:
            print("✅ 所有测试通过!")
            return True
        else:
            print("❌ 部分测试失败")
            return False
            
    except Exception as e:
        print(f"❌ 运行测试时出错: {e}")
        return False

if __name__ == "__main__":
    success = run_tests()
    sys.exit(0 if success else 1)
