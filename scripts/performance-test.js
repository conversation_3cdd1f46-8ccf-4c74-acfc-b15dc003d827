#!/usr/bin/env node

/**
 * 性能测试脚本
 * 
 * 使用Lighthouse CLI自动化测试网站性能
 * 生成详细的性能报告和建议
 * 
 * 使用方法：
 * node scripts/performance-test.js [URL] [options]
 * 
 * 示例：
 * node scripts/performance-test.js https://your-domain.com --mobile
 * node scripts/performance-test.js https://your-domain.com --desktop --output=json
 */

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

// 默认配置
const DEFAULT_CONFIG = {
    url: 'http://localhost:5000',
    mobile: true,
    desktop: false,
    output: 'html',
    outputDir: './performance-reports',
    runs: 3,
    throttling: 'devtools'
};

/**
 * 解析命令行参数
 */
function parseArgs() {
    const args = process.argv.slice(2);
    const config = { ...DEFAULT_CONFIG };
    
    // 解析URL
    if (args[0] && !args[0].startsWith('--')) {
        config.url = args[0];
    }
    
    // 解析选项
    args.forEach(arg => {
        if (arg === '--mobile') {
            config.mobile = true;
            config.desktop = false;
        } else if (arg === '--desktop') {
            config.desktop = true;
            config.mobile = false;
        } else if (arg === '--both') {
            config.mobile = true;
            config.desktop = true;
        } else if (arg.startsWith('--output=')) {
            config.output = arg.split('=')[1];
        } else if (arg.startsWith('--runs=')) {
            config.runs = parseInt(arg.split('=')[1]);
        } else if (arg.startsWith('--throttling=')) {
            config.throttling = arg.split('=')[1];
        }
    });
    
    return config;
}

/**
 * 检查Lighthouse是否已安装
 */
function checkLighthouse() {
    try {
        execSync('lighthouse --version', { stdio: 'ignore' });
        return true;
    } catch (error) {
        return false;
    }
}

/**
 * 安装Lighthouse
 */
function installLighthouse() {
    console.log('正在安装Lighthouse...');
    try {
        execSync('npm install -g lighthouse', { stdio: 'inherit' });
        console.log('Lighthouse安装成功！');
    } catch (error) {
        console.error('Lighthouse安装失败:', error.message);
        process.exit(1);
    }
}

/**
 * 创建输出目录
 */
function createOutputDir(dir) {
    if (!fs.existsSync(dir)) {
        fs.mkdirSync(dir, { recursive: true });
    }
}

/**
 * 运行Lighthouse测试
 */
function runLighthouse(url, formFactor, config) {
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const outputFile = path.join(
        config.outputDir,
        `lighthouse-${formFactor}-${timestamp}.${config.output}`
    );
    
    const command = [
        'lighthouse',
        `"${url}"`,
        `--form-factor=${formFactor}`,
        `--throttling-method=${config.throttling}`,
        '--preset=perf',
        '--chrome-flags="--headless --no-sandbox --disable-dev-shm-usage"',
        `--output=${config.output}`,
        `--output-path="${outputFile}"`
    ].join(' ');
    
    console.log(`\n🚀 运行${formFactor}端性能测试...`);
    console.log(`命令: ${command}`);
    
    try {
        const result = execSync(command, { 
            encoding: 'utf8',
            maxBuffer: 1024 * 1024 * 10 // 10MB buffer
        });
        
        console.log(`✅ ${formFactor}端测试完成`);
        console.log(`📊 报告已保存到: ${outputFile}`);
        
        return {
            success: true,
            outputFile,
            formFactor,
            result
        };
    } catch (error) {
        console.error(`❌ ${formFactor}端测试失败:`, error.message);
        return {
            success: false,
            formFactor,
            error: error.message
        };
    }
}

/**
 * 解析Lighthouse JSON报告
 */
function parseLighthouseReport(filePath) {
    try {
        const reportData = fs.readFileSync(filePath, 'utf8');
        const report = JSON.parse(reportData);
        
        const metrics = {
            performanceScore: Math.round(report.lhr.categories.performance.score * 100),
            fcp: report.lhr.audits['first-contentful-paint'].numericValue,
            lcp: report.lhr.audits['largest-contentful-paint'].numericValue,
            cls: report.lhr.audits['cumulative-layout-shift'].numericValue,
            fid: report.lhr.audits['max-potential-fid']?.numericValue || 0,
            tti: report.lhr.audits['interactive'].numericValue,
            speedIndex: report.lhr.audits['speed-index'].numericValue
        };
        
        return metrics;
    } catch (error) {
        console.warn('解析报告失败:', error.message);
        return null;
    }
}

/**
 * 生成性能摘要
 */
function generateSummary(results) {
    console.log('\n📈 性能测试摘要');
    console.log('='.repeat(50));
    
    results.forEach(result => {
        if (!result.success) {
            console.log(`\n❌ ${result.formFactor}端测试失败`);
            return;
        }
        
        console.log(`\n📱 ${result.formFactor}端结果:`);
        
        if (result.outputFile.endsWith('.json')) {
            const metrics = parseLighthouseReport(result.outputFile);
            if (metrics) {
                console.log(`   性能评分: ${metrics.performanceScore}/100`);
                console.log(`   FCP: ${Math.round(metrics.fcp)}ms`);
                console.log(`   LCP: ${Math.round(metrics.lcp)}ms`);
                console.log(`   CLS: ${metrics.cls.toFixed(3)}`);
                console.log(`   TTI: ${Math.round(metrics.tti)}ms`);
                console.log(`   Speed Index: ${Math.round(metrics.speedIndex)}`);
                
                // 性能建议
                console.log('\n💡 性能建议:');
                if (metrics.fcp > 2000) {
                    console.log('   - FCP过慢，建议优化关键资源加载');
                }
                if (metrics.lcp > 2500) {
                    console.log('   - LCP过慢，建议优化最大内容元素');
                }
                if (metrics.cls > 0.1) {
                    console.log('   - 布局偏移过大，建议为元素设置固定尺寸');
                }
                if (metrics.performanceScore < 90) {
                    console.log('   - 整体性能有待提升，请查看详细报告');
                }
            }
        }
        
        console.log(`   📄 详细报告: ${result.outputFile}`);
    });
}

/**
 * 生成对比报告
 */
function generateComparisonReport(results, config) {
    const comparisonFile = path.join(config.outputDir, 'performance-comparison.md');
    const timestamp = new Date().toLocaleString();
    
    let content = `# 性能测试报告\n\n`;
    content += `**测试时间**: ${timestamp}\n`;
    content += `**测试URL**: ${config.url}\n`;
    content += `**测试配置**: ${config.runs}次运行, ${config.throttling}节流\n\n`;
    
    results.forEach(result => {
        if (!result.success) return;
        
        content += `## ${result.formFactor}端结果\n\n`;
        
        if (result.outputFile.endsWith('.json')) {
            const metrics = parseLighthouseReport(result.outputFile);
            if (metrics) {
                content += `| 指标 | 数值 | 目标 | 状态 |\n`;
                content += `|------|------|------|------|\n`;
                content += `| 性能评分 | ${metrics.performanceScore}/100 | >90 | ${metrics.performanceScore >= 90 ? '✅' : '❌'} |\n`;
                content += `| FCP | ${Math.round(metrics.fcp)}ms | <2000ms | ${metrics.fcp < 2000 ? '✅' : '❌'} |\n`;
                content += `| LCP | ${Math.round(metrics.lcp)}ms | <2500ms | ${metrics.lcp < 2500 ? '✅' : '❌'} |\n`;
                content += `| CLS | ${metrics.cls.toFixed(3)} | <0.1 | ${metrics.cls < 0.1 ? '✅' : '❌'} |\n`;
                content += `| TTI | ${Math.round(metrics.tti)}ms | <3800ms | ${metrics.tti < 3800 ? '✅' : '❌'} |\n\n`;
            }
        }
        
        content += `**详细报告**: [${path.basename(result.outputFile)}](${result.outputFile})\n\n`;
    });
    
    fs.writeFileSync(comparisonFile, content);
    console.log(`\n📋 对比报告已生成: ${comparisonFile}`);
}

/**
 * 主函数
 */
function main() {
    console.log('🔍 ChatBI性能测试工具');
    console.log('='.repeat(30));
    
    const config = parseArgs();
    
    console.log(`\n📋 测试配置:`);
    console.log(`   URL: ${config.url}`);
    console.log(`   设备: ${config.mobile ? 'Mobile' : ''}${config.mobile && config.desktop ? ' + ' : ''}${config.desktop ? 'Desktop' : ''}`);
    console.log(`   输出格式: ${config.output}`);
    console.log(`   运行次数: ${config.runs}`);
    
    // 检查Lighthouse
    if (!checkLighthouse()) {
        console.log('\n⚠️  未检测到Lighthouse，正在安装...');
        installLighthouse();
    }
    
    // 创建输出目录
    createOutputDir(config.outputDir);
    
    // 运行测试
    const results = [];
    
    if (config.mobile) {
        const result = runLighthouse(config.url, 'mobile', config);
        results.push(result);
    }
    
    if (config.desktop) {
        const result = runLighthouse(config.url, 'desktop', config);
        results.push(result);
    }
    
    // 生成摘要和对比报告
    generateSummary(results);
    
    if (config.output === 'json') {
        generateComparisonReport(results, config);
    }
    
    console.log('\n🎉 性能测试完成！');
}

// 运行主函数
if (require.main === module) {
    main();
}
